services:
    db:
        container_name: palette-db
        image: docker.arvancloud.ir/postgres
        restart: unless-stopped
        ports:
            - "127.0.0.1:5432:5432"
        env_file: .env.local
        environment:
            - POSTGRES_DB=palette
            - POSTGRES_USER=root
            - POSTGRES_PASSWORD=root
        volumes:
            - postgres_data:/var/lib/postgresql/data
        networks:
            - local_palette_network

    redis:
        container_name: palette-cache
        image: docker.arvancloud.ir/redis
        restart: unless-stopped
        ports:
            - "127.0.0.1:6379:6379"
        env_file: .env.local
        environment:
            - ALLOW_EMPTY_PASSWORD=yes
        volumes:
            - redis_data:/data
        networks:
            - local_palette_network

    elasticsearch:
        container_name: elasticsearch
        image: docker.arvancloud.ir/elasticsearch:8.15.3
        restart: unless-stopped
        ports:
            - "127.0.0.1:9200:9200"
            - "127.0.0.1:9300:9300"
        environment:
            - discovery.type=single-node
            - ES_JAVA_OPTS=-Xms1024m -Xmx1024m
            - xpack.security.enabled=false
        volumes:
            - es_data:/usr/share/elasticsearch/data
        networks:
            - local_palette_network

    kibana:
        container_name: kibana
        image: docker.elastic.co/kibana/kibana:8.5.1
        restart: unless-stopped
        ports:
            - "127.0.0.1:5601:5601"
        environment:
            - ELASTICSEARCH_URL=http://elasticsearch:9200
        networks:
            - local_palette_network

    seaweed_master:
        container_name: palette-seaweedfs-master
        image: docker.arvancloud.ir/chrislusf/seaweedfs
        command: "master -ip=seaweed_master -ip.bind=0.0.0.0 -metricsPort=9324"
        networks:
            - local_palette_network

    seaweed_volume:
        container_name: palette-seaweedfs-volume
        image: docker.arvancloud.ir/chrislusf/seaweedfs
        volumes:
            - files:/data
        command: 'volume -mserver="seaweed_master:9333" -ip.bind=0.0.0.0 -port=8080 -metricsPort=9325'
        depends_on:
            - seaweed_master
        networks:
            - local_palette_network

    seaweed_filer:
        container_name: palette-seaweedfs-filer
        image: docker.arvancloud.ir/chrislusf/seaweedfs
        volumes:
            - files:/data
        command: 'filer -master="seaweed_master:9333" -ip.bind=0.0.0.0 -metricsPort=9326'
        depends_on:
            - seaweed_master
            - seaweed_volume   
        networks:
            - local_palette_network

volumes:
    postgres_data:
    redis_data:
    es_data:
    pgadmin_data:
    files:

networks:
    local_palette_network:
        driver: bridge