import { DataSource } from "typeorm";
import { config } from "dotenv";

if (process.env.NODE_ENV === "development") {
  config({ path: ".env.local" });
} else {
  config();
}

const { NODE_ENV, POSTGRES_DB_HOST, POSTGRES_DB_PORT, POSTGRES_DB_NAME, POSTGRES_DB_USER, POSTGRES_DB_PASSWORD } = process.env;

console.log("NODE_ENV",NODE_ENV)
console.log("POSTGRES_DB_HOST",POSTGRES_DB_HOST)
console.log("POSTGRES_DB_PORT",POSTGRES_DB_PORT)

const entitiesPatterns = NODE_ENV === "production"
    ? [
        "dist/modules/**/*.model.js",
        "dist/modules/**/*.schema.js",
        "dist/modules/**/*.entity.js"
      ]
    : [
        "modules/**/*.model.ts",
        "modules/**/*.schema.ts",
        "modules/**/*.entity.ts"
      ];

const migrationsString =
    NODE_ENV === "production" ? "dist/migrations/*.js" : "migrations/*.ts";

export default new DataSource({
    type: "postgres",
    host: POSTGRES_DB_HOST,
    port: Number(POSTGRES_DB_PORT),
    username: POSTGRES_DB_USER,
    password: POSTGRES_DB_PASSWORD,
    database: POSTGRES_DB_NAME,
    entities: entitiesPatterns,
    migrations: [migrationsString],
    synchronize: false,
    migrationsRun: false, // Don't auto-run migrations, we run them manually
    logging: NODE_ENV === "production" ? ["error", "migration"] : ["query", "error", "schema", "warn", "info", "log", "migration"],
    migrationsTableName: "migrations", // Explicitly set migration table name
});