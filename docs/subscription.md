we have some subscription plans in "subscription-plan" table
and we save users subscription in "subscription" table

0 price subscriptions mean free

we only have one free subscription for 1 month

- our plans:
free: 100 bot message per month
paid: 500 bot message per month


when user login/register we check some conditions and add free subscription to them.



we have one cronjob => which checks if 3 days remains for current subscription, we should send sms and notify user.


when user upgrades from and old plan => 
we should set old plan as expired
then add new plan + extend days from last active plan.
and set old active one as CANCELLED in status
