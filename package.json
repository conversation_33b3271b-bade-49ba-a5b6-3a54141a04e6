{"name": "palette-api", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "scripts": {"prepare": "husky", "lint": "eslint .", "lint:spell": "cspell .", "lint-staged": "lint-staged .", "lint-staged:spell": "./cspell-staged.sh", "get-staged": "git diff --name-only --cached", "dev": "NODE_ENV=development ts-node-dev --rs modules/server/index.ts", "build": "tsc", "start": "node dist/modules/server/index.js", "seed": "ts-node ./modules/seed.ts", "migration:create": "ts-node ./node_modules/.bin/typeorm migration:create", "setup-llm-db:dev": "NODE_ENV=development ts-node modules/common/lib/chatbot/scripts/setup-llm-chat-history.ts", "setup-llm-db:prod": "NODE_ENV=production ts-node modules/common/lib/chatbot/scripts/setup-llm-chat-history.ts", "migration:run:dev": "NODE_ENV=development ts-node ./node_modules/.bin/typeorm migration:run -d typeorm.config.ts", "migration:run-prod": "yarn build && NODE_ENV=production typeorm migration:run -d ./dist/typeorm.config.js", "migration:safe-run-prod": "NODE_ENV=production node scripts/safe-migrate.js", "migration:generate": "NODE_ENV=development ts-node ./node_modules/.bin/typeorm migration:generate -d typeorm.config.ts", "migration:revert": "ts-node ./node_modules/.bin/typeorm migration:revert -d typeorm.config.ts", "migration:sample": "NODE_ENV=development yarn ts-node ./node_modules/.bin/typeorm migration:generate migrations/name_.. -d typeorm.config.ts", "test": "jest", "test:unit": "jest --config=jest.config.ts", "test:e2e": "jest --config=jest.e2e.config.ts", "test:coverage": "jest --coverage", "add-admin": "node dist/modules/admin/apps/scripts/add-admin.js", "setup-pg-memory": "node dist/modules/common/lib/chatbot/scripts/setup-pg-memory.js", "drop-pg-memory": "node dist/modules/common/lib/chatbot/scripts/drop-pg-memory.js"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@jest/types": "^29.6.3", "@types/chai": "^4.3.16", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.5", "@types/mocha": "^10.0.6", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.14.8", "@types/passport": "^1.0.16", "@types/passport-google-oauth": "^1.0.45", "@types/passport-instagram": "^1.0.5", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.11.10", "@types/sinon": "^17.0.3", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "chai": "^5.1.1", "copyfiles": "^2.4.1", "cspell": "^8.9.1", "eslint": "^9.5.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.2.0", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "lint-staged": "^15.2.7", "mocha": "^10.4.0", "prettier": "^3.3.2", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.2", "typescript-eslint": "^7.13.1"}, "dependencies": {"@bull-board/api": "^6.9.6", "@bull-board/express": "^6.9.6", "@elastic/elasticsearch": "^8.15.0", "@faker-js/faker": "^8.4.1", "@jest/types": "^29.6.3", "@langchain/community": "^0.3.12", "@langchain/google-vertexai": "^0.0.27", "@langchain/langgraph": "^0.2.20", "@langchain/langgraph-checkpoint-postgres": "^0.0.2", "@langchain/openai": "^0.3.11", "@types/multer": "^1.4.11", "ajv": "^8.16.0", "ajv-formats": "^3.0.1", "bcrypt": "^5.1.1", "bullmq": "^5.8.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-async-errors": "^3.1.1", "express-session": "^1.18.0", "faker": "^6.6.6", "firebase-admin": "^12.6.0", "global-agent": "^3.0.0", "i18next": "^23.13.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "kavenegar-client": "^0.0.4", "langchain": "^0.3.5", "langfuse-langchain": "^3.37.4", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-instagram": "^1.0.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "pg-protocol": "^1.6.1", "prom-client": "^15.1.3", "sanitize-filename": "^1.6.3", "sharp": "^0.33.5", "swagger-ui-express": "^5.0.1", "ts-jest": "^29.2.5", "tsyringe": "^4.8.0", "typeorm": "^0.3.20", "uuid": "^10.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.5"}}