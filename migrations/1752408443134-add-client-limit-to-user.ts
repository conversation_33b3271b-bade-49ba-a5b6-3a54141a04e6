import { MigrationInterface, QueryRunner } from "typeorm";

export class AddClientLimitToUser1752408443134 implements MigrationInterface {
    name = 'AddClientLimitToUser1752408443134'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "clientLimit" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "clientLimit"`);
    }

}
