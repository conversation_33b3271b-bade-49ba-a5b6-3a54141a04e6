import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveChatbotEnableChannel1752678013984 implements MigrationInterface {
    name = 'RemoveChatbotEnableChannel1752678013984'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "chatbotEnabled"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" ADD "chatbotEnabled" boolean NOT NULL DEFAULT true`);
    }

}
