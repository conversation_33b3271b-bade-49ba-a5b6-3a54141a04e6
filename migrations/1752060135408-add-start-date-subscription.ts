import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStartDateSubscription1752060135408 implements MigrationInterface {
    name = 'AddStartDateSubscription1752060135408'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription" ADD "startDate" TIMESTAMP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription" DROP COLUMN "startDate"`);
    }

}
