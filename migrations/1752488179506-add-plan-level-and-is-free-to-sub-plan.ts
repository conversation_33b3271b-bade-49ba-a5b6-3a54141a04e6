import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPlanLevelAndIsFreeToSubPlan1752488179506 implements MigrationInterface {
    name = 'AddPlanLevelAndIsFreeToSubPlan1752488179506'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "transaction" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "type" character varying NOT NULL, "status" character varying NOT NULL DEFAULT 'PENDING', "amount" numeric(15,2) NOT NULL, "currency" character varying NOT NULL DEFAULT 'IRR', "paymentMethod" character varying NOT NULL, "trackId" character varying, "subscriptionPlanId" integer, "description" character varying, "meta" json, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_89eadb93a89810556e1cbcd6ab9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "subscription-plan" ADD "planLevel" character varying`);
        await queryRunner.query(`ALTER TABLE "subscription-plan" ADD "isFree" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD CONSTRAINT "FK_605baeb040ff0fae995404cea37" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD CONSTRAINT "FK_37820e715861dc2690106875c1b" FOREIGN KEY ("subscriptionPlanId") REFERENCES "subscription-plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transaction" DROP CONSTRAINT "FK_37820e715861dc2690106875c1b"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP CONSTRAINT "FK_605baeb040ff0fae995404cea37"`);
        await queryRunner.query(`ALTER TABLE "subscription-plan" DROP COLUMN "isFree"`);
        await queryRunner.query(`ALTER TABLE "subscription-plan" DROP COLUMN "planLevel"`);
        await queryRunner.query(`DROP TABLE "transaction"`);
    }

}
