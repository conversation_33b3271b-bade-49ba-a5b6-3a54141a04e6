import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLanguageToUser1751267922400 implements MigrationInterface {
    name = 'AddLanguageToUser1751267922400'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "language" character varying NOT NULL DEFAULT 'fa'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "language"`);
    }

}
