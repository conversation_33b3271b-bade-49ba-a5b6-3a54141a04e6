version: "3.9"

services:
  db_main:
    container_name: palette_db_main
    image: docker.arvancloud.ir/postgres
    restart: always
    volumes:
      - palette_db_main:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=${POSTGRES_DB_NAME}
      - POSTGRES_USER=${POSTGRES_DB_USER}
      - POSTGRES_PASSWORD=${POSTGRES_DB_PASSWORD}
    ports:
      - "127.0.0.1:5432:5432"
    networks:
      - palette_net_main

  cache_main:
    container_name: palette_cache_main
    image: docker.arvancloud.ir/redis
    restart: always
    volumes:
      - palette_cache_main:/data
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - palette_net_main

  elasticsearch_main:
    container_name: palette_elasticsearch_main
    image: docker.arvancloud.ir/elasticsearch:8.15.3
    restart: always
    volumes:
      - palette_data_main:/usr/share/elasticsearch/data
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms1g -Xmx2g
    networks:
      - palette_net_main

  kibana_main:
    container_name: palette_kibana_main
    image: docker.elastic.co/kibana/kibana:8.5.1
    restart: always
    ports:
      - "127.0.0.1:5601:5601"
    environment:
      - ELASTICSEARCH_URL=http://elasticsearch_main:9200
    depends_on:
      - elasticsearch_main
    networks:
      - palette_net_main

  api_main:
    container_name: palette_api_main
    image: palette/api
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    depends_on:
      - db_main
      - cache_main
      - elasticsearch_main
    ports:
      - "3000:3000"
    command: ["sh", "-c", "yarn migration:safe-run-prod"]
    environment:
      - NODE_ENV=${NODE_ENV}
      - PROJECT=${PROJECT}
      - DOMAIN=${DOMAIN}
      - PORT=${PORT}
      - WEB_APP_URL=${WEB_APP_URL}
      - VIRTUAL_HOST=${VIRTUAL_HOST}
      - LETSENCRYPT_HOST=${LETSENCRYPT_HOST}
      - VIRTUAL_PORT=${VIRTUAL_PORT}
      - ENABLE_CONSOLE_LOG=${ENABLE_CONSOLE_LOG}
      - ENABLE_PROMETHEUS_LOG=${ENABLE_PROMETHEUS_LOG}
      - LOG_FILE_PATH=${LOG_FILE_PATH}
      - POSTGRES_DB_HOST=${POSTGRES_DB_HOST}
      - POSTGRES_DB_PORT=${POSTGRES_DB_PORT}
      - POSTGRES_DB_NAME=${POSTGRES_DB_NAME}
      - POSTGRES_DB_USER=${POSTGRES_DB_USER}
      - POSTGRES_DB_PASSWORD=${POSTGRES_DB_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CHATBOT=${CHATBOT}
      - EMBEDDING=${EMBEDDING}
      - ELASTIC_URI=${ELASTIC_URI}
      - SALT_ROUNDS=${SALT_ROUNDS}
      - SESSION_SECRET=${SESSION_SECRET}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PREFIX=${REDIS_PREFIX}
      - FILER_URL=${FILER_URL}
      - ZIBAL_MERCHANT_ID=${ZIBAL_MERCHANT_ID}
      - ZIBAL_PREFIX_URL=${ZIBAL_PREFIX_URL}
      - ZIBAL_CALL_BACK_URL=${ZIBAL_CALL_BACK_URL}
      - PAYMENT_LINK_PREFIX_URL=${PAYMENT_LINK_PREFIX_URL}
      - ZIBAL_KEY=${ZIBAL_KEY}
      - NAVASAN_KEY=${NAVASAN_KEY}
      - KAVENEGAR_API_KEY=${KAVENEGAR_API_KEY}
      - INSTAGRAM_CLIENT_ID=${INSTAGRAM_CLIENT_ID}
      - INSTAGRAM_CLIENT_SECRET=${INSTAGRAM_CLIENT_SECRET}
      - INSTAGRAM_CALLBACK_URL=${INSTAGRAM_CALLBACK_URL}
      - INSTAGRAM_WEBHOOK_URL=${INSTAGRAM_WEBHOOK_URL}
      - INSTAGRAM_WEBHOOK_TOKEN=${INSTAGRAM_WEBHOOK_TOKEN}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_USERINFO_URL=${GOOGLE_USERINFO_URL}
      - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - STATIC_SERVER_URL=${STATIC_SERVER_URL}
      - TEST_USERS=${TEST_USERS}
      - LANGFUSE_HOST_URL=${LANGFUSE_HOST_URL}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - PLATFORM_ACCOUNT_USER_ID=${PLATFORM_ACCOUNT_USER_ID}
    networks:
      - palette_net_main

  seaweed_master_main:
    container_name: palette_seaweedfs_master_main
    image: docker.arvancloud.ir/chrislusf/seaweedfs
    command: "master -ip=seaweed_master_main -ip.bind=0.0.0.0 -metricsPort=9324"
    networks:
      - palette_net_main

  seaweed_volume_main:
    container_name: palette_seaweedfs_volume_main
    image: docker.arvancloud.ir/chrislusf/seaweedfs
    volumes:
      - palette_files_main:/data
    command: 'volume -mserver="seaweed_master_main:9333" -ip.bind=0.0.0.0 -port=8080 -metricsPort=9325'
    depends_on:
      - seaweed_master_main
    networks:
      - palette_net_main

  seaweed_filer_main:
    container_name: palette_seaweedfs_filer_main
    image: docker.arvancloud.ir/chrislusf/seaweedfs
    volumes:
      - palette_files_main:/data
    command: 'filer -master="seaweed_master_main:9333" -ip.bind=0.0.0.0 -metricsPort=9326'
    depends_on:
      - seaweed_master_main
      - seaweed_volume_main
    networks:
      - palette_net_main

networks:
  palette_net_main:
    external: true
    name: backend_nginx_proxy_main

volumes:
  palette_db_main:
  palette_cache_main:
  palette_files_main:
  palette_data_main:
