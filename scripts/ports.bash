#!/bin/bash

# List of ports used by containers
PORTS=(5432 6379 9200 9300 5601 8080 9324 9325 9326)

echo "Killing processes on the following ports: ${PORTS[*]}"

for PORT in "${PORTS[@]}"; do
    PIDS=$(lsof -ti tcp:$PORT)

    if [ -n "$PIDS" ]; then
        echo "Killing processes on port $PORT (PID(s): $PIDS)..."
        kill -9 $PIDS
    else
        echo "No process found on port $PORT."
    fi
done

echo "Done."
