#!/usr/bin/env node

const { spawn } = require('child_process');

async function runCommand(command, args) {
    return new Promise((resolve, reject) => {
        console.log(`Running: ${command} ${args.join(' ')}`);
        const process = spawn(command, args, { stdio: 'inherit' });
        
        process.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with exit code ${code}`));
            }
        });
        
        process.on('error', reject);
    });
}

async function checkPendingMigrations() {
    return new Promise((resolve, reject) => {
        console.log('Checking for pending migrations...');
        const process = spawn('npx', ['typeorm', 'migration:show', '-d', './dist/typeorm.config.js'], { 
            stdio: ['inherit', 'pipe', 'inherit'] 
        });
        
        let output = '';
        process.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        process.on('close', (code) => {
            if (code === 0) {
                // Check if there are any pending migrations
                const hasPending = output.includes('[ ]') || output.includes('No migrations found');
                resolve(!hasPending); // Return true if no pending migrations
            } else {
                reject(new Error(`Migration check failed with exit code ${code}`));
            }
        });
        
        process.on('error', reject);
    });
}

async function main() {
    try {
        console.log('🔍 Checking migration status...');
        
        // First, build the project
        await runCommand('yarn', ['build']);
        
        // Check if there are pending migrations
        const noPendingMigrations = await checkPendingMigrations();
        
        if (noPendingMigrations) {
            console.log('✅ No pending migrations found. Skipping migration run.');
        } else {
            console.log('📦 Pending migrations found. Running migrations...');
            await runCommand('npx', ['typeorm', 'migration:run', '-d', './dist/typeorm.config.js']);
            console.log('✅ Migrations completed successfully.');
        }
        
        console.log('🚀 Starting application...');
        await runCommand('yarn', ['start']);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

main();
