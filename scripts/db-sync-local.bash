#!/bin/bash

# Database backup script from server to local
# Server DB: localhost:5455
# Local DB: localhost:5432

set -e  # Exit on any error

# Database connection parameters
SERVER_HOST="localhost"
SERVER_PORT="5455"
SERVER_USER="root"
SERVER_PASSWORD="root"
SERVER_DB="palette"

LOCAL_HOST="localhost"
LOCAL_PORT="5432"
LOCAL_USER="root"
LOCAL_PASSWORD="root"
LOCAL_DB="palette"

SCHEMA="public"
BACKUP_FILE="/tmp/db_backup_$(date +%Y%m%d_%H%M%S).sql"

echo "Starting database backup from server to local..."
echo "Backup file: $BACKUP_FILE"

# Export password for pg_dump
export PGPASSWORD="$SERVER_PASSWORD"

echo "Creating backup from server database..."
pg_dump -h "$SERVER_HOST" -p "$SERVER_PORT" -U "$SERVER_USER" -d "$SERVER_DB" \
    --schema="$SCHEMA" --no-owner --no-privileges --clean --if-exists \
    -f "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "Backup created successfully!"
else
    echo "Error: Failed to create backup from server database"
    exit 1
fi

# Export password for psql
export PGPASSWORD="$LOCAL_PASSWORD"

echo "Restoring backup to local database..."
psql -h "$LOCAL_HOST" -p "$LOCAL_PORT" -U "$LOCAL_USER" -d "$LOCAL_DB" \
    -f "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "Database restored successfully to local!"
    echo "Cleaning up backup file..."
    rm "$BACKUP_FILE"
    echo "Backup process completed successfully!"
else
    echo "Error: Failed to restore backup to local database"
    echo "Backup file preserved at: $BACKUP_FILE"
    exit 1
fi