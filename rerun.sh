#!/bin/bash

set -e

# Step 1: Ensure .env file exists in current directory
ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
  echo "❌ Environment file does not exist in the current directory: $ENV_FILE"
  exit 1
fi

# Step 2: Restart with sudo if not root
if [ "$(id -u)" -ne 0 ]; then
  echo "⚠️  Restarting with sudo..."
  exec sudo "$0" "$@"
fi

# Step 3: Load env vars from .env in current directory
echo "📦 Loading environment from $ENV_FILE"
set -a
source "$ENV_FILE"
set +a

# Step 4: Validate required environment variables
MISSING=0
[ -z "$VIRTUAL_HOST" ]       && echo "❌ VIRTUAL_HOST is not set" && MISSING=1
[ -z "$LETSENCRYPT_HOST" ]   && echo "❌ LETSENCRYPT_HOST is not set" && MISSING=1
[ -z "$VIRTUAL_PORT" ]       && echo "❌ VIRTUAL_PORT is not set" && MISSING=1

if [ "$MISSING" -eq 1 ]; then
  echo "❌ Missing one or more required environment variables"
  exit 1
fi

echo "🔗 VIRTUAL_HOST=$VIRTUAL_HOST"
echo "🔒 LETSENCRYPT_HOST=$LETSENCRYPT_HOST"
echo "🌐 VIRTUAL_PORT=$VIRTUAL_PORT"

# Step 5: Ensure external proxy network exists
NET_NAME="backend_nginx_proxy_main"
if ! docker network inspect "$NET_NAME" &>/dev/null; then
  echo "🌐 Creating external network $NET_NAME..."
  docker network create "$NET_NAME"
else
  echo "🌐 External network $NET_NAME already exists."
fi

# Step 6: Compose details
COMPOSE_FILE="docker-compose.yml"
PROJECT_NAME="palette_main"

echo "📄 Using compose file: $COMPOSE_FILE"
echo "📦 Project name: $PROJECT_NAME"

# Step 7: Tear down old stack
echo "🛑 Stopping existing containers..."
docker compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down

# Step 8: Build & up
echo "🚀 Building and starting containers..."
docker compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up --build -d

echo "✅ $PROJECT_NAME is up and running!"