import sharp from "sharp";

import Uploader from "../uploader";

export class ImageStrategy extends Uploader {
    constructor() {
        super({
            maxSize: 2 * 1000 * 1000,
            mimeTypes: [
                "image/jpeg", 
                "image/png", 
                "image/gif", 
                "image/avif", 
                "image/webp"
            ],
            folderName: "images",
        });
    }

    async toAVIF(file: Express.Multer.File) {
        const buffer = await sharp(file.buffer).avif().toBuffer();

        file.buffer = buffer;

        const ext = file.originalname.slice(file.originalname.lastIndexOf("."));
        file.originalname = file.originalname.replace(ext, ".avif");

        file.size = buffer.byteLength;

        return file;
    }

    async upload(file: Express.Multer.File) {
        const avifImage = await this.toAVIF(file);

        return super.upload(avifImage);
    }
}
