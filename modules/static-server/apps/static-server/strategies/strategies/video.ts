import Uploader from "../uploader";

export class VideoStrategy extends Uploader {
    constructor() {
        super({
            maxSize: 25 * 1000 * 1000, // 25MB limit for videos
            mimeTypes: [
                "video/mp4",
                "video/mpeg", 
                "video/quicktime", // MOV
                "video/x-msvideo", // AVI
                "video/x-ms-wmv", // WMV
                "video/webm",
                "video/ogg",
                "video/3gpp", // 3GP
                "video/x-flv", // FLV
                "video/x-matroska", // MKV
                "video/x-ms-asf", // ASF
                "video/x-f4v", // F4V
            ],
            folderName: "videos",
        });
    }

    async upload(file: Express.Multer.File) {
        return super.upload(file);
    }
}
