import Uploader from "../uploader";

export class AudioStrategy extends Uploader {
    constructor() {
        super({
            maxSize: 10 * 1000 * 1000, // 10MB limit for audio
            mimeTypes: [
                "audio/mpeg", // MP3
                "audio/mp4", // M4A, AAC
                "audio/wav",
                "audio/ogg",
                "audio/webm",
                "audio/flac",
                "audio/aac",
                "audio/x-ms-wma", // WMA
                "audio/x-wav",
                "audio/vnd.wav",
                "audio/3gpp", // 3GP audio
                "audio/amr", // AMR
                "audio/opus",
                "audio/x-m4b", // M4B (audiobook)
                "audio/aiff",
                "audio/x-aiff",
                "audio/basic", // AU
            ],
            folderName: "audio",
        });
    }

    async upload(file: Express.Multer.File) {
        return super.upload(file);
    }
}
