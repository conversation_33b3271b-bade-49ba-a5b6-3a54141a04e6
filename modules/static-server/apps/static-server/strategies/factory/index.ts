import { ImageStrategy, PDFStrategy, VideoStrategy, AudioStrategy } from "../strategies";

export class FileUploaderFactory {
    static createUploader(fileType: string) {
        let strategy = null;

        if (new ImageStrategy().getMimeTypes().includes(fileType)) {
            strategy = new ImageStrategy();
        }
        if (new PDFStrategy().getMimeTypes().includes(fileType)) {
            strategy = new PDFStrategy();
        }
        if (new VideoStrategy().getMimeTypes().includes(fileType)) {
            strategy = new VideoStrategy();
        }
        if (new AudioStrategy().getMimeTypes().includes(fileType)) {
            strategy = new AudioStrategy();
        }

        return strategy;
    }
}
