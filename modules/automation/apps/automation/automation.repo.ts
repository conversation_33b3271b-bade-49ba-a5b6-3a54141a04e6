import { layers } from "../../../common";
import { Automation } from "./types";
import { AutomationSchema } from "./automation.model";

export default class AutomationRepo extends layers.BaseTypeormRepository<Automation> {
    relations = ["condition", "action"];
    constructor() {
        super(AutomationSchema);
    }

    getAutomations = async (
        userId: number,
        query: Partial<Express.Query>,
    ): Promise<Automation[]> => {
        return this._repo
            .createQueryBuilder("automation")
            .where("automation.userId = :userId", { userId })
            .leftJoinAndSelect(
                "condition",
                "condition",
                "condition.automationId = automation.id",
            )
            .leftJoinAndSelect(
                "action",
                "action",
                "action.automationId = automation.id",
            )
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getAutomation = async (id: number, userId: number) => {
        return this._repo
            .createQueryBuilder("automation")
            .where("automation.id = :id", { id })
            .andWhere("automation.userId = :userId", { userId })
            .leftJoinAndSelect(
                "condition",
                "condition",
                "condition.automationId = automation.id",
            )
            .leftJoinAndSelect(
                "action",
                "action",
                "action.automationId = automation.id",
            )
            .getRawMany();
    };

    getUserAutomations = async (userId: number): Promise<Automation[]> => {
        return this._repo
            .createQueryBuilder("automation")
            .where("automation.userId = :userId", { userId })
            .andWhere("automation.isActive = :isActive", { isActive: true })
            .leftJoinAndSelect(
                "condition",
                "condition",
                "condition.automationId = automation.id",
            )
            .leftJoinAndSelect(
                "action",
                "action",
                "action.automationId = automation.id",
            )
            .getRawMany();
    };
}
