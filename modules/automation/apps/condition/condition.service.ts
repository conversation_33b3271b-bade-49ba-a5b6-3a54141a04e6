import { injectable } from "tsyringe";
import ConditionRepo from "./condition.repo";
import { errors, utils } from "../../../common";
import { ConditionDto } from "./types";

import { EntityManager } from "typeorm";

import Logger from "../../../common/lib/metrics/logger";
import {
    conditionInvalidErrorLog,
    conditionRemoveLog,
    conditionResetLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";

@injectable()
export default class ConditionsService {
    constructor(private _repo: ConditionRepo) {}

    validateConditions = (conditions: ConditionDto[]) => {
        const existingTypes = conditions.map((condition) => condition.type);

        if (existingTypes.includes("followed") && existingTypes.length === 1) {
            Logger.error("Invalid condition: FOLLOWED alone is not valid", {
                conditionTypes: existingTypes,
                action: "validateConditions",
            });
            conditionInvalidErrorLog.inc();
            recordErrorValue(
                "InvalidConditionError",
                "FOLLOWED alone is not valid",
            );

            throw new errors.InvalidConditionError(
                "FOLLOWED alone is not a valid condition.",
            );
        }

        if (
            existingTypes.includes("contain") &&
            existingTypes.includes("exactly_equal")
        ) {
            Logger.error(
                "Invalid condition: CONTAIN and EXACTLY_EQUAL cannot be used together",
                {
                    conditionTypes: existingTypes,
                    action: "validateConditions",
                },
            );
            conditionInvalidErrorLog.inc();
            recordErrorValue(
                "InvalidConditionError",
                "CONTAIN and EXACTLY_EQUAL cannot be used together",
            );

            throw new errors.InvalidConditionError(
                "CONTAIN and EXACTLY_EQUAL cannot be used together.",
            );
        }
    };

    resetConditions = async (
        conditions: ConditionDto[],
        automationId: number,
        manager: EntityManager,
    ) => {
        await this._repo.deleteByQuery(
            {
                automationId,
            },
            { manager },
        );

        await this._repo.bulkCreate(
            conditions.map((condition) => ({
                ...condition,
                automationId,
            })),
            { manager },
        );

        conditionResetLog.inc();
        Logger.info("Conditions reset successfully", {
            automationId,
            action: "resetConditions",
        });
    };

    removeConditions = async (automationId: number, manager: EntityManager) => {
        await this._repo.deleteByQuery(
            {
                automationId,
            },
            { manager },
        );

        conditionRemoveLog.inc();
        Logger.info("Conditions removed successfully", {
            automationId,
            action: "removeConditions",
        });
    };

    processCondition = (condition: ConditionDto, text: string) => {
        Logger.info("Processing condition", { actionType: condition.type });

        if (condition.type === "exactly_equal") {
            return condition.keyword!.toLowerCase() === text.toLowerCase();
        } else if (condition.type === "contain") {
            return utils.rtlToLtrNumber(text)
                .includes(
                    utils.rtlToLtrNumber(condition.keyword!),
                );
        } else if (condition.type === "followed") {
            // TODO: IMPLEMENT
            return true;
        }
    };
}
