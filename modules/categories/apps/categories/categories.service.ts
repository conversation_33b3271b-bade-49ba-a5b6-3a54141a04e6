import { injectable } from "tsyringe";
import CategoryRepo from "./categories.repo";

import { AddCategoryDto, EditCategoryDto } from "./types";

import { errors, utils } from "../../../common";
import { getCategoriesSerializer } from "./responses";
import Logger from "../../../common/lib/metrics/logger";
import {
    categoryNotFoundErrorLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";
import { AppLanguage, CategoryHierarchy } from "../../../common/base/types/typing";


@injectable()
export default class CategoryService {
    constructor(private _repo: CategoryRepo) {}

    getCategoryOfUser = async (id: number, userId: number) => {
        const category = await this._repo.findOneByQuery({
            userId,
            id,
        });

        if (utils.isNil(category)) {
            categoryNotFoundErrorLog.inc();
            Logger.error("not found error", {
                error: "Category not found",
                statusCode: 404,
            });

            recordErrorValue("not found error", "Category not found");
            throw new errors.NotFoundError("Category");
        }
        return category;
    };

    addCategory = async (body: AddCategoryDto, user: Express.User) => {
        const { parentId } = body;
        const { id: userId } = user;

        if (utils.isNotNil(parentId)) {
            await this.getCategoryOfUser(parentId, userId);
        }

        await this._repo.create({
            ...body,
            userId,
        });
    };

    editCategory = async (
        body: EditCategoryDto,
        user: Express.User,
        id: number,
    ) => {
        const { parentId } = body;
        const { id: userId } = user;

        if (utils.isNotNil(parentId)) {
            await this.getCategoryOfUser(parentId, userId);
        }

        const isUpdated = await this._repo.updateOneByQuery(
            { id, userId },
            body,
        );

        if (!isUpdated) {
            throw new errors.NotFoundError("Category");
        }
    };

    getCategories = async (
        parsedQuery: Partial<Express.Query>,
        user: Express.User,
    ) => {
        const { id: userId } = user;

        const categories = await this._repo.getCategoriesForUser(
            userId,
            parsedQuery,
        );

        return getCategoriesSerializer(categories);
    };

    deleteCategory = async (id: number, user: Express.User) => {
        const { id: userId } = user;

        const isDeleted = await this._repo.deleteOneByQuery({
            userId,
            id,
        });

        if (!isDeleted) {
            throw new errors.NotFoundError("Category");
        }
    };


    getCategoriesForPrompt = async (userId: number, language: AppLanguage = "en", limit: number = 10): Promise<string> => {
        try {
            console.log(`[CategoryService] Getting parent categories for user ${userId} in language ${language}`);

            // Get only parent categories for the user with limit applied at database level
            const parentCategories = await this._repo.getParentCategoriesForUser(userId, limit);

            if (!parentCategories || parentCategories.length === 0) {
                console.log(`[CategoryService] No parent categories found for user ${userId}`);
                return language === "fa"
                    ? "هیچ دسته‌بندی والد تعریف نشده است."
                    : "No parent categories are defined.";
            }

            console.log(`[CategoryService] Found ${parentCategories.length} parent categories for user ${userId}`);

            // Format parent categories as simple list
            const formattedCategories = this._formatParentCategoriesForPrompt(parentCategories, language);

            console.log(`[CategoryService] Formatted categories: ${formattedCategories}`);
            return formattedCategories;

        } catch (error) {
            console.error(`[CategoryService] Error getting parent categories for user ${userId}:`, error);
            return language === "fa"
                ? "خطا در بارگیری دسته‌بندی‌ها."
                : "Error loading categories.";
        }
    };


    private _buildCategoryHierarchy = (categories: any[]): CategoryHierarchy[] => {
        const categoryMap = new Map<number, CategoryHierarchy>();
        const rootCategories: CategoryHierarchy[] = [];

        // First pass: create all category nodes
        categories.forEach(category => {
            categoryMap.set(category.id, {
                id: category.id,
                name: category.name,
                children: []
            });
        });

        // Second pass: build hierarchy
        categories.forEach(category => {
            const categoryNode = categoryMap.get(category.id)!;

            if (category.parentId && categoryMap.has(category.parentId)) {
                // This is a child category
                const parent = categoryMap.get(category.parentId)!;
                parent.children.push(categoryNode);
            } else {
                // This is a root category
                rootCategories.push(categoryNode);
            }
        });

        return rootCategories;
    };

    private _formatParentCategoriesForPrompt = (categories: any[], language: AppLanguage): string => {
        if (categories.length === 0) {
            return language === "fa"
                ? "هیچ دسته‌بندی تعریف نشده است."
                : "No parent categories are defined.";
        }

        const formattedCategories = categories
            .map(category => `- ${category.name}`)
            .join("\n");

        const introduction = language === "fa"
            ? "دسته‌بندی‌های والد موجود در فروشگاه:\n"
            : "Available parent categories in our store:\n";

        return `${introduction}${formattedCategories}`;
    };
}
