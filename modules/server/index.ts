import "reflect-metadata";
import { config } from "dotenv";

if (process.env.NODE_ENV === "development") {
    config({ path: ".env.local" });
} else {
    config();
}
import { typeorm, utils } from "../common/";

import app from "./app";
import { container } from "tsyringe";
import RatesService from "../rates/apps/rates/rates.service";
import ChannelsService from "../channels/apps/channels/channel.service";
import { initializeI18n } from "../common/lib/errors/locales";
import ZaraScraperService from "../scrapers/apps/zara-scraper/zara-scraper.service";
import { SubscriptionSchedulerService } from "../subscriptions/apps/scheduler";
// Bull Board imports removed - not using UI for now
// import { ExpressAdapter } from "@bull-board/express";
// import { createBullBoard } from "@bull-board/api";
// import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { registerChatbotServices } from "../common/lib/chatbot/register-services";

const ratesService = container.resolve(RatesService);
const zaraScraperService = container.resolve(ZaraScraperService);
const channelsService = container.resolve(ChannelsService);
const subscriptionSchedulerService = container.resolve(SubscriptionSchedulerService);

const REQUIRED_ENV_VARS = [
    "NODE_ENV",
    "PROJECT",
    "DOMAIN",
    "PORT",
    "WEB_APP_URL",
    "POSTGRES_DB_HOST",
    "POSTGRES_DB_PORT",
    "POSTGRES_DB_NAME",
    "POSTGRES_DB_PASSWORD",
    "REDIS_HOST",
    "REDIS_PORT",
    "REDIS_PREFIX",
    "SALT_ROUNDS",
    "ELASTIC_URI",
    "SESSION_SECRET",
    "JWT_SECRET",
    "INSTAGRAM_CLIENT_SECRET",
    "INSTAGRAM_CLIENT_ID",
    "INSTAGRAM_CALLBACK_URL",
    "GOOGLE_CLIENT_ID",
    "GOOGLE_CLIENT_SECRET",
    "GOOGLE_USERINFO_URL",
    "NAVASAN_KEY",
    "KAVENEGAR_API_KEY",
    "FILER_URL",
    "ZIBAL_MERCHANT_ID",
    "ZIBAL_PREFIX_URL",
    "ZIBAL_CALL_BACK_URL",
    "PAYMENT_LINK_PREFIX_URL",
    "ZIBAL_KEY",
    "WEB_APP_URL",
    "GOOGLE_APPLICATION_CREDENTIALS",
    "GOOGLE_CLOUD_PROJECT",
    "ENABLE_CONSOLE_LOG",
    "LOG_FILE_PATH",
    "ENABLE_PROMETHEUS_LOG",
    "CHATBOT",
    "EMBEDDING",
    "STATIC_SERVER_URL",
    "PLATFORM_ACCOUNT_USER_ID",
];

const { PORT } = process.env;

const start = async () => {
    for (const envVar of REQUIRED_ENV_VARS) {
        if (utils.isNil(process.env[envVar])) {
            throw new Error(`${envVar} must be defined!`);
        }
    }

    await initializeI18n();

    await typeorm.AppDataSource.getInstance().initialize();

    // Register chatbot services
    registerChatbotServices();

    await ratesService.scheduleLiraPriceJob();
    // await zaraScraperService.initializeJobs();
    await channelsService.initSubscribingToEvents();
    await subscriptionSchedulerService.initializeScheduledJobs();

    // Bull Board UI setup removed - not using for now
    // const serverAdapter = new ExpressAdapter();
    // serverAdapter.setBasePath("/api/v1/queues");
    // createBullBoard({
    //     queues: [
    //         new BullMQAdapter(zaraScraperService.queue.getQueue()),
    //         new BullMQAdapter(channelsService.subscribeToEventsQueue.getQueue()),
    //     ],
    //     serverAdapter,
    // });
    // app.use("/api/v1/queues", serverAdapter.getRouter());

    app.listen(PORT, () => {
        console.log(`Listening on port ${PORT}!`);
    });
};

const stop = async () => {
    await ratesService.closeJob();
    await zaraScraperService.closeJob();
};

start().catch(console.error);

process.on("SIGTERM", () => {
    console.log("SIGTERM signal received.");
    stop()
        .then(() => {
            console.log("Successfully stopped!");
            return process.exit(0);
        })
        .catch((error) => {
            console.error(error);
            process.exit(-1);
        });
});

process.on("SIGINT", () => {
    console.log("SIGINT signal received.");
    stop()
        .then(() => {
            console.log("Successfully stopped!");
            return process.exit(0);
        })
        .catch((error) => {
            console.error(error);
            process.exit(-1);
        });
});
