import { inject, injectable, registry } from "tsyringe";
import UserRepo from "./users.repo";

import { GoogleAuthDto, LoginDto, SendOtp, VerifyLoginOtpDto } from "./types";

import { auth, errors, utils } from "../../../common";
import { REGISTRATION_METHOD } from "../../../common/base/types/typing";
import { InstagramService } from "./../../../common/lib/instagram";
import { RedisConnection } from "../../../common/lib/redis";
import Redis from "ioredis";
import { registries } from "./registries";
import { GoogleOAuthService, OTPService } from "../../../common/lib/auth";
import { KavenegarService } from "../../../common/lib/sms";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { Job, Processor } from "bullmq";
import { getUserSerializer, getUsersSerializer } from "./responses";
import ClientService from "../client/client.service";
import ChatService from "../../../chat/apps/chat/chat.service";
import ActionsService from "../../../automation/apps/action/action.service";
import UserSubscriptionService from "../../../subscriptions/apps/user-subscriptions/user-subscription.service";
import Logger from "../../../common/lib/metrics/logger";
import {
    expiredOtpErrorLog,
    googleAuthFailureLog,
    googleAuthNewUserLog,
    googleAuthSuccessLog,
    otpVerifiedLog,
    otpVerifiedTestUserLog,
    userAddLog,
    userLoginLog,
    userNotFoundErrorLog,
    userOtpSentLog,
    wrongOtpErrorLog,
    wrongOtpTestUserErrorLog,
} from "../../../common/lib/metrics/metrics";
import { TestUsersService } from "../../../common/lib/utils/test-users";

@registry(registries)
@injectable()
export default class UsersService {
    private _sendSmsQueue: BullMQModule<SendOtp>;
    private _sendSmsJobName: string = "send-sms";
    private _loginOtpFieldName = "login-otp";
    private _redisConnection: RedisConnection;
    private _smsService: KavenegarService;
    private _redis: Redis;

    constructor(
        private _repo: UserRepo,
        private _instagramService: InstagramService,
        private _clientService: ClientService,
        private _chatService: ChatService,
        private _actionService: ActionsService,
        private _userSubscriptionService: UserSubscriptionService,
        private _googleAuthService: GoogleOAuthService,

        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
        @inject("sms")
        createSmsService: () => KavenegarService,
    ) {
        this._redisConnection = createRedisConnection();
        this._redis = this._redisConnection.getConnection();
        this._smsService = createSmsService();
        const processor: Processor<SendOtp> = createJobProcessor(
            this._sendOtp.bind(this),
        );
        this._sendSmsQueue = createQueueModule(
            this._sendSmsJobName,
            processor,
            this._redisConnection,
        );
    }

    getUserRegistrationReport = async (parsedQuery: Partial<Express.Query>) => {
        const { filter } = parsedQuery;
        if (utils.isNil(filter)) throw new errors.BadRequestError();

        const { from, to } = filter;
        const { startDate, endDate } = this.parseAndValidateInputs(from, to);

        const users = await this._repo.getDailyAndAggregatedUserRegistrations(
            startDate,
            endDate,
        );

        return {
            data: users,
            metadata: { from: startDate, to: endDate },
        };
    };

    private parseAndValidateInputs = (from?: string, to?: string) => {
        if (utils.isNil(from) || utils.isNil(to))
            throw new errors.BadRequestError();

        const startDate = new Date(from);
        const endDate = new Date(to);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime()))
            throw new errors.BadRequestError();

        return { startDate, endDate };
    };

    private createFreeSubscriptionForUser = async (
        userId: number,
        context: string,
        additionalData?: Record<string, any>,
    ) => {
        try {
            await this._userSubscriptionService.createFreeSubscriptionForUser(
                userId,
            );
        } catch (error) {
            Logger.error(
                `Failed to create free subscription for user - ${context}`,
                {
                    userId,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                    ...additionalData,
                },
            );
        }
    };

    private _sendOtp = async (job: Job<SendOtp>) => {
        const { phone, otp } = job.data;
        userOtpSentLog.inc();
        Logger.info("[UsersService] Attempting to send OTP", {
            phone,
            otp,
            queueName: this._sendSmsJobName,
        });
        try {
            const result = await this._smsService.sendOtp(phone, otp);
            Logger.info("[UsersService] OTP sent successfully", {
                phone,
                otp,
                result,
            });
            return result;
        } catch (error: any) {
            Logger.error("[UsersService] Failed to send OTP", {
                phone,
                otp,
                errorMessage: error?.message || "Unknown error",
                queueName: this._sendSmsJobName,
            });
            throw error;
        }
    };

    login = async (body: LoginDto) => {
        const { phone } = body;

        let user = null;

        user = await this._repo.findOneByQuery({
            phone,
        });

        if (utils.isNil(user)) {
            user = await this._repo.create({
                phone,
                registrationMethod: REGISTRATION_METHOD.PHONE,
                language: "fa", // Explicitly set default language
            });
            userAddLog.inc();
            Logger.info("User created successfully", { phone });

            // Create free subscription for new user
        }
        await this.createFreeSubscriptionForUser(
            user.id,
            "new user registration",
            { phone },
        );

        // Special handling for test users
        if (TestUsersService.isTestUser(user.phone)) {
            const testPassword = TestUsersService.getTestUserPassword(
                user.phone,
            );
            Logger.info("Using fixed OTP for test user", {
                phone: user.phone,
                isTestUser: true,
            });
            const otp = OTPService.generateOTPObject();
            otp.value = testPassword || process.env.FIX_PASS || "99999";
            return otp;
        }

        // Normal flow for regular users
        const otp = OTPService.generateOTPObject();
        await this._redis.setex(
            `${this._loginOtpFieldName}:${user.id}`,
            otp.expirationInSeconds,
            otp.value,
        );
        await this._sendSmsQueue.addJob(this._sendSmsJobName, {
            phone,
            otp: otp.value,
        });

        userLoginLog.inc();
        Logger.info("Login OTP generated and sent", { phone, userId: user.id });

        return otp;
    };

    getUser = async (userId: number) => {
        const user = await this._repo.findOneByQuery({ id: userId });
        if (utils.isNil(user)) {
            userNotFoundErrorLog.inc();
            Logger.error("User not found", { userId });
            throw new errors.NotFoundError("User");
        }
        return user;
    };

    verifyLoginOtp = async (body: VerifyLoginOtpDto) => {
        const { otp, phone } = body;

        // First, get the user regardless of whether it's a fixed user or not
        const user = await this._repo.findOneByQuery({
            phone,
        });

        if (utils.isNil(user)) {
            userNotFoundErrorLog.inc();
            Logger.error("User not found during OTP verification", { phone });
            throw new errors.NotFoundError("User");
        }

        // Special handling for test users
        if (TestUsersService.isTestUser(phone)) {
            const testPassword = TestUsersService.getTestUserPassword(phone);
            if (testPassword && otp === testPassword) {
                Logger.info("Test user authenticated successfully", {
                    phone,
                    userId: user.id,
                    isTestUser: true,
                });
                otpVerifiedTestUserLog.inc();

                // Create free subscription for test user if not exists
                await this.createFreeSubscriptionForUser(
                    user.id,
                    "test user authentication",
                    { phone, isTestUser: true },
                );

                return {
                    token: auth.signJWT({
                        id: user.id,
                        role: user.role,
                    }),
                };
            } else {
                wrongOtpTestUserErrorLog.inc();
                Logger.error("Incorrect OTP for test user", {
                    phone,
                    userId: user.id,
                    isTestUser: true,
                });
                throw new errors.WrongOtpError();
            }
        }

        // Normal flow for regular users
        const otpValue = await this._redis.get(
            `${this._loginOtpFieldName}:${user.id}`,
        );
        if (utils.isNil(otpValue)) {
            expiredOtpErrorLog.inc();
            Logger.error("Expired OTP", { userId: user.id });
            throw new errors.ExpiredOtpError();
        }

        if (otp !== otpValue) {
            wrongOtpErrorLog.inc();
            Logger.error("Incorrect OTP", { userId: user.id });
            throw new errors.WrongOtpError();
        }

        await this._redis.del(`${this._loginOtpFieldName}:${user.id}`);
        otpVerifiedLog.inc();
        Logger.info("OTP verified successfully", { userId: user.id });

        // Create free subscription for user if not exists
        await this.createFreeSubscriptionForUser(user.id, "OTP verification", {
            phone,
        });

        return {
            token: auth.signJWT({
                id: user.id,
                role: user.role,
            }),
        };
    };

    async googleAuth(args: GoogleAuthDto) {
        try {
            const data = await this._googleAuthService.verifyGoogleToken(
                args.token,
            );

            console.log("Looking up user by email:", data.email);
            let user = await this._repo.findOneByQuery({
                email: data.email,
            });
            console.log(
                "User lookup result:",
                user ? `Found user ID: ${user.id}` : "User not found",
            );

            if (utils.isNil(user)) {
                console.log("Creating new user with email:", data.email);
                user = await this._repo.create({
                    email: data.email,
                    registrationMethod: REGISTRATION_METHOD.GOOGLE,
                    language: "fa", // Explicitly set default language
                });
                console.log("New user created with ID:", user.id);
                googleAuthNewUserLog.inc();
                Logger.info("New user created via Google authentication", {
                    email: data.email,
                    userId: user.id,
                });
                googleAuthSuccessLog.inc();

                // Create free subscription for new user
                await this.createFreeSubscriptionForUser(
                    user.id,
                    "new Google user",
                    { email: data.email },
                );

                const token = auth.signJWT({ id: user.id, role: user.role });
                return { token };
            }

            console.log(
                "Checking user registration method:",
                user.registrationMethod,
            );
            if (user.registrationMethod !== REGISTRATION_METHOD.GOOGLE) {
                console.log(
                    "ERROR: User registration method mismatch. Expected GOOGLE, got:",
                    user.registrationMethod,
                );
                Logger.error(
                    "Incorrect login method for Google authentication",
                    {
                        email: data.email,
                        userId: user.id,
                        registrationMethod: user.registrationMethod,
                    },
                );
                googleAuthFailureLog.inc();
                throw new errors.IncorrectLoginMethodError();
            }

            Logger.info("User authenticated via Google successfully", {
                email: data.email,
                userId: user.id,
            });
            googleAuthSuccessLog.inc();

            // Create free subscription for existing Google user if not exists
            await this.createFreeSubscriptionForUser(
                user.id,
                "existing Google user",
                { email: data.email },
            );

            const token = auth.signJWT({ id: user.id, role: user.role });
            return { token };
        } catch (error) {
            googleAuthFailureLog.inc();

            Logger.error("Google authentication failed", {
                error: error instanceof Error ? error.message : "Unknown error",
                code: args.token
                    ? `${args.token.substring(0, 10)}...`
                    : "undefined",
            });
            throw error;
        }
    }

    getUserProfile = async (
        user: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = user;

        const userProfile = await this._repo.getProfileForUser(
            userId,
            parsedQuery,
        );

        const profile = getUserSerializer(userProfile);
        const currentSubscription =
            await this._userSubscriptionService.getCurrentSubscriptionOfUser(
                userId,
            );

        // Convert CurrentSubscriptionRow to GetUserSubscriptionResponse format
        if (currentSubscription) {
            profile.subscription = {
                id: currentSubscription.id,
                status: currentSubscription.status,
                startDate: currentSubscription.startDate.toISOString(),
                endDate: currentSubscription.endDate.toISOString(),
                plan: currentSubscription.plan,
            };
        } else {
            profile.subscription = null;
        }

        Logger.info("User profile retrieved successfully", { userId });

        return profile;
    };

    getUsers = async (parsedQuery: Partial<Express.Query>) => {
        const usersProfile = await this._repo.getProfileForUsers(parsedQuery);

        if (utils.isNil(usersProfile) || usersProfile.length === 0) {
            userNotFoundErrorLog.inc();
            Logger.error("No users found matching query", {
                query: parsedQuery,
            });

            throw new errors.NotFoundError("Users");
        }

        return getUsersSerializer(usersProfile);
    };

    // Instagram Webhook
    // processInstagramWebhook = async (
    //     body: InstagramWebhookPayloadDto,
    // ): Promise<void> => {
    //     if (body.object !== "instagram") {
    //         return;
    //     }

    //     for (const entry of body.entry) {
    //         const messagingEvents = entry.messaging;

    //         for (const event of messagingEvents) {
    //             if (event.message && event.message.is_echo === false) {
    //                 const clientId = event.sender.id;
    //                 const userId = event.recipient.id;
    //                 const messageText = event.message.text;

    //                 await this.processInstagramMessaging(
    //                     userId,
    //                     clientId,
    //                     messageText!,
    //                 );

    //                 await this.processFormResponse(clientId, messageText!);
    //             }
    //         }
    //     }
    // };

    //!! What is this using for ?
    async processInstagramMessaging(
        pageId: string, //The ID for your Facebook Page
        clientId: string, // Id of sender
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        messageText: string,
    ) {
        // TODO: accessToken is should be get from input not env
        const accessToken = process.env.PAGE_ACCESS_TOKEN ?? "";
        const platform = "instagram";

        //------------  first strategy look for sender name  ------------//
        // look for properties of MessagingEventDto to save user name

        //------------ second strategy look for sender name  ------------//
        // Fetch the conversation with the specific user
        const conversation =
            await this._instagramService.getConversationWithUser(
                pageId,
                platform,
                clientId,
                accessToken,
            );

        if (
            utils.isNotNil(conversation) &&
            utils.isNotNil(conversation.data) &&
            conversation.data.length > 0
        ) {
            const conversationId = conversation.data[0].id;

            // Fetch messages in the conversation
            const messages =
                await this._instagramService.getMessagesInConversation(
                    conversationId,
                    accessToken,
                );

            // Process each message
            if (utils.isNotNil(messages) && utils.isNotNil(messages.data)) {
                for (const message of messages.data) {
                    // const messageId = message.id;
                    // const createdTime = message.created_time;
                    // const messageText = message.text;
                    // Logger.info("Processing Instagram message", {
                    //     messageId: message.id,
                    //     createdTime: message.created_time,
                    //     messageText: message.text,
                    // });
                    // console.log(`Message ID: ${messageId}`);
                    // console.log(`Created Time: ${createdTime}`);
                    // console.log(`Message Text: ${messageText}`);
                }
            }
        }

        // TODO: save sender details (don't use client repo make create method in clientService)
        // instagramUsername = sender.name;
        // userId = recipientId;
        // await this._ClientRepo.CreateClientDto({
        //     instagramUsername: "instagramUsername",
        //     userId: 2,
        // });

        // TODO: save chat history (don't add client user which exist just add message)
        // await this._chatService.addChatHistory(clientId, userId, messageText)
    }
}
