import { injectable, inject, registry } from "tsyringe";
import { RedisConnection } from "../../../common/lib/redis";
import UserRepo from "../users/users.repo";
import Logger from "../../../common/lib/metrics/logger";
import { ClientLimitResult } from "./types";
import { registries } from "./registries";

@registry(registries)
@injectable()
export default class ClientLimitService {
    private _redisConnection: RedisConnection;

    constructor(
        private _userRepo: UserRepo,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();
    }

    /**
     * Check if a client can interact with the user's chatbot
     * @param userId - The user (seller) ID
     * @param clientPlatformId - The client's platform ID
     * @returns Promise<ClientLimitResult>
     */
    async checkClientLimit(userId: number, clientPlatformId: string): Promise<ClientLimitResult> {
        try {
            // Get user's client limit from database
            const user = await this._userRepo.findOneByQuery({ id: userId });
            
            if (!user) {
                Logger.error("User not found for client limit check", { userId });
                return {
                    isAllowed: false,
                    isNewClient: false,
                    currentCount: 0,
                    limit: null,
                    remainingSlots: null,
                };
            }

            const clientLimit = user.clientLimit;

            // If no limit is set (null), allow unlimited clients
            if (clientLimit === null || clientLimit === 0) {
                const isNewClient = !(await this.isClientActive(userId, clientPlatformId));
                return {
                    isAllowed: true,
                    isNewClient,
                    currentCount: await this.getActiveClientsCount(userId),
                    limit: clientLimit,
                    remainingSlots: null, // unlimited
                };
            }

            // Check if client is already active
            const isClientActive = await this.isClientActive(userId, clientPlatformId);
            
            if (isClientActive) {
                // Existing client, always allowed
                return {
                    isAllowed: true,
                    isNewClient: false,
                    currentCount: await this.getActiveClientsCount(userId),
                    limit: clientLimit,
                    remainingSlots: Math.max(0, clientLimit - await this.getActiveClientsCount(userId)),
                };
            }

            // New client - check if we have space
            const currentCount = await this.getActiveClientsCount(userId);
            const isAllowed = currentCount < clientLimit;

            return {
                isAllowed,
                isNewClient: true,
                currentCount,
                limit: clientLimit,
                remainingSlots: Math.max(0, clientLimit - currentCount),
            };

        } catch (error) {
            Logger.error("Error checking client limit", { userId, clientPlatformId, error });
            return {
                isAllowed: false,
                isNewClient: false,
                currentCount: 0,
                limit: null,
                remainingSlots: null,
            };
        }
    }

    /**
     * Add a client to the active clients set
     * @param userId - The user (seller) ID
     * @param clientPlatformId - The client's platform ID
     */
    async addClient(userId: number, clientPlatformId: string): Promise<void> {
        try {
            const redis = this._redisConnection.getConnection();
            const key = this.getActiveClientsKey(userId);
            
            // Add client to set
            await redis.sadd(key, clientPlatformId);
            
            // Set TTL to 30 days (can be configured)
            const ttlSeconds = 30 * 24 * 60 * 60; // 30 days
            await redis.expire(key, ttlSeconds);

            Logger.info("Client added to active clients", { userId, clientPlatformId });
        } catch (error) {
            Logger.error("Error adding client to active clients", { userId, clientPlatformId, error });
        }
    }

    /**
     * Check if a client is already active for a user
     * @param userId - The user (seller) ID
     * @param clientPlatformId - The client's platform ID
     * @returns Promise<boolean>
     */
    async isClientActive(userId: number, clientPlatformId: string): Promise<boolean> {
        try {
            const redis = this._redisConnection.getConnection();
            const key = this.getActiveClientsKey(userId);
            
            const isMember = await redis.sismember(key, clientPlatformId);
            return isMember === 1;
        } catch (error) {
            Logger.error("Error checking if client is active", { userId, clientPlatformId, error });
            return false;
        }
    }

    /**
     * Get the count of active clients for a user
     * @param userId - The user (seller) ID
     * @returns Promise<number>
     */
    async getActiveClientsCount(userId: number): Promise<number> {
        try {
            const redis = this._redisConnection.getConnection();
            const key = this.getActiveClientsKey(userId);
            
            return await redis.scard(key);
        } catch (error) {
            Logger.error("Error getting active clients count", { userId, error });
            return 0;
        }
    }

    /**
     * Get all active clients for a user
     * @param userId - The user (seller) ID
     * @returns Promise<string[]>
     */
    async getActiveClients(userId: number): Promise<string[]> {
        try {
            const redis = this._redisConnection.getConnection();
            const key = this.getActiveClientsKey(userId);
            
            return await redis.smembers(key);
        } catch (error) {
            Logger.error("Error getting active clients", { userId, error });
            return [];
        }
    }

    /**
     * Reset all active clients for a user
     * @param userId - The user (seller) ID
     */
    async resetUserClients(userId: number): Promise<void> {
        try {
            const redis = this._redisConnection.getConnection();
            const key = this.getActiveClientsKey(userId);
            
            await redis.del(key);
            Logger.info("User clients reset", { userId });
        } catch (error) {
            Logger.error("Error resetting user clients", { userId, error });
        }
    }

    /**
     * Remove a specific client from active clients
     * @param userId - The user (seller) ID
     * @param clientPlatformId - The client's platform ID
     */
    async removeClient(userId: number, clientPlatformId: string): Promise<void> {
        try {
            const redis = this._redisConnection.getConnection();
            const key = this.getActiveClientsKey(userId);
            
            await redis.srem(key, clientPlatformId);
            Logger.info("Client removed from active clients", { userId, clientPlatformId });
        } catch (error) {
            Logger.error("Error removing client from active clients", { userId, clientPlatformId, error });
        }
    }

    // Note: Client limit CRUD operations are handled manually in database
    // No API endpoints needed for now

    /**
     * Generate Redis key for user's active clients
     * @param userId - The user (seller) ID
     * @returns string
     */
    private getActiveClientsKey(userId: number): string {
        return `palette:user-active-clients:${userId}`;
    }
}
