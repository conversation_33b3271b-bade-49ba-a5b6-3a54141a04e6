import { delay, inject, injectable, registry } from "tsyringe";
import OrderRepo from "./order.repo";
import {
    AddOrderDto,
    EditOrderDto,
    EditOrderStatusDto,
    ExchangeRateType,
    OrderInventoryDto,
    OrderItemDto,
    OrderStatus,
} from "./types";
import { EntityManager } from "typeorm";
import InventoryService from "../../../inventories/apps/inventories/inventories.service";
import OrderItemsService from "../../../order-items/apps/order-items/order-items.service";
import ChannelService from "../../../channels/apps/channels/channel.service";
import ClientService from "../../../users/apps/client/client.service";
import {
    getClientOrderSerializer,
    getClientOrdersSerializer,
    getOrdersSerializer,
} from "./responses";
import { errors, utils } from "../../../common";
import {
    detectTimePeriod,
    generateDateRange,
} from "../../../common/lib/utils/dates";
import Logger from "../../../common/lib/metrics/logger";
import {
    editOrderLog,
    getOrdersLog,
    newOrderCreationLog,
    orderNotFoundErrorLog,
    recordErrorValue,
    sellerOrderNotificationSentLog,
} from "../../../common/lib/metrics/metrics";
import { registries } from "../../../users/apps/users/registries";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { SendSellerNotification } from "../../../users/apps/users/types";
import { Job, Processor } from "bullmq";
import { RedisConnection } from "../../../common/lib/redis";
import { KavenegarService } from "../../../common/lib/sms";
import { TestUsersService } from "../../../common/lib/utils/test-users";
import UsersService from "../../../users/apps/users/users.service";
import NotificationService from "../../../notifications/apps/notifications/notification.service";

const INTERVALS = ["daily", "weekly", "monthly", "yearly"];

@registry(registries)
@injectable()
export default class OrdersService {
    private _sendSmsQueue: BullMQModule<SendSellerNotification>;
    private _sendSmsJobName: string = "send-seller-notification";
    private _redisConnection: RedisConnection;
    private _smsService: KavenegarService;

    constructor(
        private _repo: OrderRepo,
        private _orderItemsService: OrderItemsService,
        @inject(delay(() => InventoryService))
        private _inventoryService: InventoryService,
        @inject(delay(() => ChannelService))
        private _channelService: ChannelService,
        private _clientService: ClientService,
        private _userService: UsersService,
        private _notificationService: NotificationService,
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
        @inject("sms")
        createSmsService: () => KavenegarService,
    ) {
        this._redisConnection = createRedisConnection();
        this._smsService = createSmsService();
        const processor: Processor<SendSellerNotification> = createJobProcessor(
            this._sendSellerNotification.bind(this),
        );
        this._sendSmsQueue = createQueueModule(
            this._sendSmsJobName,
            processor,
            this._redisConnection,
        );
    }

    private _sendSellerNotification = async (
        job: Job<SendSellerNotification>,
    ) => {
        const { phone, orderId, userName } = job.data;

        sellerOrderNotificationSentLog.inc();
        Logger.info("[OrdersService] Attempting to send seller notification", {
            phone,
            orderId,
            queueName: this._sendSmsJobName,
        });
        try {
            const result = await this._smsService.sendSellerNotification(
                phone,
                orderId,
                userName,
            );
            Logger.info(
                "[OrdersService] Seller notification sent successfully",
                { phone, orderId, result },
            );
            return result;
        } catch (error: any) {
            Logger.error("[OrdersService] Failed to send seller notification", {
                phone,
                orderId,
                errorMessage: error?.message || "Unknown error",
            });
            throw error;
        }
    };

    private _calculateTotalPrice = (inventories: OrderItemDto[]) => {
        const totalPrice = inventories.reduce((acc, { price, amount }) => {
            // Ensure price is in a format that can be converted to BigInt
            let priceStr = price ?? "0";

            // Remove decimal points if present
            if (priceStr.includes(".")) {
                console.log(
                    `[_calculateTotalPrice] Converting price with decimal: ${priceStr}`,
                );
                priceStr = priceStr.replace(".", "");
            }

            try {
                return acc + BigInt(priceStr) * BigInt(amount);
            } catch (error) {
                console.error(
                    `[_calculateTotalPrice] Error converting price to BigInt: ${priceStr}`,
                    error,
                );
                return acc;
            }
        }, 0n);

        return totalPrice;
    };

    private _calculateTotalCost = (inventories: OrderItemDto[]) => {
        const totalCost = inventories.reduce((acc, { cost, amount }) => {
            // Ensure cost is in a format that can be converted to BigInt
            let costStr = cost ?? "0";

            // Remove decimal points if present
            if (costStr.includes(".")) {
                console.log(
                    `[_calculateTotalCost] Converting cost with decimal: ${costStr}`,
                );
                costStr = costStr.replace(".", "");
            }

            try {
                return acc + BigInt(costStr) * BigInt(amount);
            } catch (error) {
                console.error(
                    `[_calculateTotalCost] Error converting cost to BigInt: ${costStr}`,
                    error,
                );
                return acc;
            }
        }, 0n);

        return totalCost;
    };

    private _generateMergedInventories = async (
        inventories: OrderInventoryDto[],
        userId: number,
    ) => {
        const inventoryIds = inventories.map(({ id }) => id);
        const inventoryItems =
            await this._inventoryService.getInventoriesOfUserByIds(
                userId,
                inventoryIds,
            );
        const mergedInventories = utils.mergeArraysByKey(
            inventoryItems,
            inventories,
            "id",
            ["id", "name", "amount", "cost", "price", "total", "reserved"],
        ) as OrderItemDto[];

        return mergedInventories;
    };

    private _editInventoryStock = async (
        prevInventories: OrderItemDto[],
        inventories: OrderItemDto[],
        manager: EntityManager,
    ) => {
        await this._inventoryService.editStock(
            inventories.map(({ id, total, reserved, amount, ...rest }) => {
                const currentInventory = prevInventories.find(
                    (inventory) => inventory.id === id,
                );

                const newReserved =
                    reserved + amount - (currentInventory?.amount ?? 0);

                Logger.info("inventory edit stock", {
                    inventoryId: id,
                    total,
                    previousAmount: currentInventory?.amount ?? 0,
                    newAmount: amount,
                    previousReserved: reserved,
                    newReserved,
                });

                return {
                    id,
                    total,
                    reserved: newReserved,
                    ...rest,
                };
            }),
            manager,
        );
    };

    private _editInventoryStatus = async (
        inventories: OrderItemDto[],
        prevStatus: string,
        status: string,
        manager: EntityManager,
    ) => {
        if (
            status !== OrderStatus.PENDING_SELLER_REVIEW &&
            prevStatus === OrderStatus.PENDING_SELLER_REVIEW
        ) {
            await this._inventoryService.editStock(
                inventories.map(({ total, reserved, amount, ...rest }) => {
                    Logger.info("edit inventory status", {
                        action: "editInventoryStatus",
                        entity: "order",
                        prevStatus,
                        prevReserved: reserved,
                        newReserved: reserved - amount,
                        prevTotal: total,
                        newTotal: total - amount,
                    });

                    return {
                        total: total - amount,
                        reserved: reserved - amount,
                        ...rest,
                    };
                }),
                manager,
            );
        }
    };

    getClientActiveOrder = async (clientId: number) => {
        const rawOrder = await this._repo.getClientActiveOrder(clientId);

        return getClientOrderSerializer(rawOrder);
    };

    getClientOrders = async (clientId: number) => {
        const rawOrder = await this._repo.getClientOrders(clientId);

        return getClientOrdersSerializer(rawOrder);
    };

    manageOrder = async (
        userId: number,
        clientId: number,
        inventory: OrderInventoryDto,
    ) => {
        const channel =
            await this._channelService.getChannelOfUserByUserId(userId);
        const { exchangeRateType, exchangeRateValue } = channel;

        const exchangeRateTypeValue: ExchangeRateType =
            exchangeRateType as ExchangeRateType;

        const client = await this._clientService.getClientOfUser(
            clientId,
            userId,
        );

        const { phone } = await this._userService.getUser(userId);

        const currentOrder = await this._repo.findOneByQuery({
            clientId,
            status: OrderStatus.PENDING_SELLER_REVIEW,
        });

        const orderItems = utils.isNotNil(currentOrder)
            ? (
                  await this._orderItemsService.getOrderItems(currentOrder.id)
              ).map((item) => ({
                  id: item.inventoryId,
                  amount: item.amount,
              }))
            : [];
        const mergedOrderItems = await this._generateMergedInventories(
            orderItems.map(({ id, amount }) => ({
                id,
                amount,
            })),
            userId,
        );

        const existingOrderItemIndex = orderItems.findIndex(
            (item) => item.id === inventory.id,
        );

        if (existingOrderItemIndex !== -1) {
            orderItems[existingOrderItemIndex].amount += inventory.amount;
            if (orderItems[existingOrderItemIndex].amount <= 0) {
                orderItems.splice(existingOrderItemIndex, 1);
            }
        } else {
            if (inventory.amount > 0) {
                orderItems.push(inventory);
            }
        }

        const mergedInventories = await this._generateMergedInventories(
            orderItems.map(({ id, amount }) => ({
                id,
                amount,
            })),
            userId,
        );

        const totalCost = this._calculateTotalCost(mergedInventories);
        const totalPrice = this._calculateTotalPrice(mergedInventories);

        const profit = totalPrice - totalCost;

        const order = await this._repo.runTransaction(
            async (manager: EntityManager) => {
                let order = null;
                if (utils.isNotNil(currentOrder)) {
                    order = currentOrder;
                    await this._repo.updateById(currentOrder.id, {
                        profit: profit.toString(),
                        price: totalPrice.toString(),
                        cost: totalCost.toString(),
                    });
                } else {
                    order = await this._repo.create(
                        {
                            userId,
                            clientId,
                            profit: profit.toString(),
                            exchangeRateType: exchangeRateTypeValue,
                            exchangeRateValue,
                            price: totalPrice.toString(),
                            cost: totalCost.toString(),
                            ...(utils.isNotNil(clientId)
                                ? {
                                      receiver: client.username,
                                  }
                                : {}),
                        },
                        { manager },
                    );
                }

                await this._orderItemsService.resetOrderItems(
                    mergedInventories.map(({ id, amount, cost, price }) => ({
                        inventoryId: id,
                        amount,
                        cost,
                        price,
                    })),
                    order.id,
                    manager,
                );

                await this._editInventoryStock(
                    mergedOrderItems,
                    mergedInventories,
                    manager,
                );

                return order;
            },
        );

        //Send SMS Notification to seller
        try {
            const orderId = order.id.toString();
            Logger.info(
                "[OrdersService] Adding seller notification job to queue",
                {
                    phone,
                    orderId,
                    queueName: this._sendSmsJobName,
                    orderDetails: {
                        id: order.id,
                        price: order.price,
                        status: order.status,
                        clientId: order.clientId,
                    },
                },
            );

            // Make sure phone is not empty or undefined, and skip if phone is a test user
            if (!phone) {
                Logger.error(
                    "[OrdersService] Cannot send SMS notification: phone number is empty",
                    { userId, clientId },
                );
            } else if (TestUsersService.isTestUser(phone)) {
                Logger.info(
                    "[OrdersService] Skipping SMS notification: phone number is test user",
                    { userId, clientId, phone, isTestUser: true },
                );
            } else {
                await this._sendSmsQueue.addJob(this._sendSmsJobName, {
                    phone: phone,
                    orderId: orderId,
                    userName: client.username,
                });
                Logger.info(
                    "[OrdersService] Successfully added seller notification job to queue",
                    { queueName: this._sendSmsJobName },
                );
            }
        } catch (error: any) {
            Logger.error(
                "[OrdersService] Failed to add seller notification job to queue",
                {
                    error: error?.message || "Unknown error",
                    stack: error?.stack,
                    queueName: this._sendSmsJobName,
                },
            );
        }

        await this._notificationService.addNewOrderNotification(
            userId,
            order.id,
        );
    };

    addOrder = async (args: AddOrderDto, profile: Express.User) => {
        const { id: userId } = profile;
        const { inventories, clientId, ...order } = args;

        const mergedInventories = await this._generateMergedInventories(
            inventories,
            userId,
        );

        const totalCost = this._calculateTotalCost(mergedInventories);
        const totalPrice = this._calculateTotalPrice(mergedInventories);

        const channel =
            await this._channelService.getChannelOfUserByUserId(userId);
        const { exchangeRateType, exchangeRateValue } = channel;

        const exchangeRateTypeValue: ExchangeRateType =
            exchangeRateType as ExchangeRateType;

        const client = utils.isNotNil(clientId)
            ? await this._clientService.getClientOfUser(clientId, userId)
            : null;

        const profit = totalPrice - totalCost;
        return await this._repo.runTransaction(
            async (manager: EntityManager) => {
                const createdOrder = await this._repo.create(
                    {
                        ...order,
                        userId,
                        clientId,
                        profit: profit.toString(),
                        exchangeRateType: exchangeRateTypeValue,
                        exchangeRateValue,
                        price: totalPrice.toString(),
                        cost: totalCost.toString(),
                        ...(utils.isNotNil(clientId)
                            ? {
                                  receiver: client!.username,
                              }
                            : {}),
                    },
                    { manager },
                );

                await this._editInventoryStock([], mergedInventories, manager);

                await this._orderItemsService.resetOrderItems(
                    mergedInventories.map(({ id, amount, cost, price }) => ({
                        inventoryId: id,
                        amount,
                        cost,
                        price,
                    })),
                    createdOrder.id,
                    manager,
                );

                newOrderCreationLog.inc();
                Logger.info("new order is created", {
                    userId,
                    action: "addOrder",
                    statusCode: 200,
                    args: order,
                    profit,
                    price: totalPrice,
                    cost: totalCost,
                    exchangeRateType,
                    exchangeRateValue,
                });
            },
        );
    };

    getOrders = async (
        user: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = user;

        const orders = await this._repo.getOrdersOfUser(userId, parsedQuery);

        getOrdersLog.inc();
        Logger.info("get orders", {
            userId,
            action: "getOrders",
        });

        return getOrdersSerializer(orders);
    };

    getOrderOfUserById = async (id: number, userId: number) => {
        const order = await this._repo.findOneByQuery({ id, userId });

        if (utils.isNil(order)) {
            orderNotFoundErrorLog.inc();
            Logger.error("get order of user by id", {
                userId,
                action: "getOrderOfUserById",
                orderId: id,
            });

            recordErrorValue("not found error", "Order not found");
            throw new errors.NotFoundError("Order");
        }

        return order;
    };

    editOrder = async (id: number, user: Express.User, args: EditOrderDto) => {
        const { id: userId } = user;
        const { inventories, ...order } = args;

        const existingOrder = await this.getOrderOfUserById(id, userId);

        //!! We should talk about logic of this section
        // if (existingOrder.status !== OrderStatus.PENDING_SELLER_REVIEW) {
        //     Logger.error(
        //         "edit Order existing.status is not PENDING_SELLER_REVIEW",
        //         {
        //             userId,
        //             action: "editOrder",
        //             statusCode: 400,
        //             error: "badRequestError",
        //         },
        //     );

        //     recordErrorValue("badRequestError", "");
        //     throw new errors.BadRequestError();
        // }

        const orderItems = await this._orderItemsService.getOrderItems(id);

        const mergedOrderItems = await this._generateMergedInventories(
            orderItems.map(({ inventoryId, amount }) => ({
                id: inventoryId,
                amount,
            })),
            userId,
        );

        let mergedInventories: OrderItemDto[] | [] = [];
        if (utils.isNotNil(inventories)) {
            mergedInventories = await this._generateMergedInventories(
                inventories,
                userId,
            );
        }

        let totalPrice = existingOrder.price;
        let totalCost = existingOrder.cost;
        let profit = existingOrder.profit;

        if (utils.isNotNil(inventories)) {
            totalPrice =
                this._calculateTotalPrice(mergedInventories).toString();
            totalCost = this._calculateTotalCost(mergedInventories).toString();
            profit = (BigInt(totalPrice) - BigInt(totalCost)).toString();
        }

        await this._repo.runTransaction(async (manager: EntityManager) => {
            if (utils.isNotNil(order)) {
                await this._repo.updateById(
                    id,
                    {
                        ...order,
                        price: totalPrice,
                        cost: totalCost,
                        profit,
                    },
                    {
                        manager,
                    },
                );
            }

            if (utils.isNotNil(inventories)) {
                await this._orderItemsService.resetOrderItems(
                    mergedInventories.map(({ id, amount, cost, price }) => ({
                        inventoryId: id,
                        amount,
                        cost,
                        price,
                    })),
                    id,
                    manager,
                );

                await this._editInventoryStock(
                    mergedOrderItems,
                    mergedInventories,
                    manager,
                );
            }

            editOrderLog.inc();
            Logger.info("edit order", {
                orderId: id,
                args: order,
            });
        });
    };

    editOrderStatus = async (
        id: number,
        user: Express.User,
        args: EditOrderStatusDto,
    ) => {
        const { id: userId } = user;
        const { status } = args;

        const order = await this.getOrderOfUserById(id, userId);
        const orderItems = await this._orderItemsService.getOrderItems(id);

        const mergedOrderItems = await this._generateMergedInventories(
            orderItems.map(({ inventoryId, amount }) => ({
                id: inventoryId,
                amount,
            })),
            userId,
        );

        await this._repo.runTransaction(async (manager: EntityManager) => {
            await this._editInventoryStatus(
                mergedOrderItems,
                order.status,
                status,
                manager,
            );
            await this._repo.updateById(order.id, { status }, { manager });

            Logger.info("edit order status", {
                orderId: order.id,
                success: true,
                statusCode: 200,
                prevStatus: order.status,
                newStatus: status,
            });
        });

        // TODO: send message to user!
        await this._channelService.notifyOrderStatus(
            userId,
            order.clientId,
            status,
        );
    };

    getConversionRate = async (
        profile: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = profile;

        if (utils.isNil(parsedQuery.filter)) {
            Logger.error(
                "order conversion rate: parsedQuery filter is not sent",
                {
                    userId,
                    statusCode: 400,
                    error: "badRequestError",
                },
            );

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        const { from, to, interval } = parsedQuery.filter;

        if (utils.isNil(from)) {
            Logger.error("order conversion rate: 'from' is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");

            throw new errors.BadRequestError();
        }
        if (utils.isNil(to)) {
            Logger.error("order conversion rate: 'to' is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
        if (!INTERVALS.includes(interval)) {
            Logger.error(
                "order conversion rate: 'interval' is not a part of INTERVALS is not sent",
                {
                    userId,
                    statusCode: 400,
                    error: "badRequestError",
                },
            );

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        const data = await this._repo.getConversionRate(
            userId,
            from,
            to,
            interval,
        );

        return {
            data,
            metadata: {
                from,
                to,
                interval,
            },
        };
    };

    getOrdersCount = async (
        profile: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = profile;

        if (utils.isNil(parsedQuery.filter)) {
            Logger.error("order orders count: parsedQuery filter is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        const { from, to } = parsedQuery.filter;

        if (utils.isNil(from)) {
            Logger.error("get orders count: 'from' is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
        if (utils.isNil(to)) {
            Logger.error("get orders count: 'to' is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        // Parse the dates
        const fromDate = new Date(from);
        const toDate = new Date(to);

        // Check if the dates are valid
        if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
            Logger.error("get orders count: invalid date format", {
                userId,
                statusCode: 400,
                error: "badRequestError",
                from,
                to,
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        // Detect time period and adjust date range if needed
        const timePeriod = detectTimePeriod(fromDate, toDate);
        let adjustedFrom = from;
        let adjustedTo = to;

        if (timePeriod) {
            // If a specific time period is detected, adjust the date range
            // Use toDate as the reference date (today)
            const dateRange = generateDateRange(toDate, timePeriod);

            // Format dates to ISO string
            adjustedFrom = dateRange.from.toISOString();
            adjustedTo = dateRange.to.toISOString();

            Logger.info(
                `Detected time period: ${timePeriod}, adjusting date range`,
                {
                    userId,
                    originalFrom: from,
                    originalTo: to,
                    adjustedFrom,
                    adjustedTo,
                },
            );
        }

        const data = await this._repo.getOrdersCount(
            userId,
            adjustedFrom,
            adjustedTo,
        );

        return {
            data,
            metadata: {
                from: adjustedFrom,
                to: adjustedTo,
            },
        };
    };

    getRetentionRate = async (
        profile: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = profile;

        if (utils.isNil(parsedQuery.filter)) {
            Logger.error(
                "order retention rate: parsedQuery filter is not sent",
                {
                    userId,
                    statusCode: 400,
                    error: "badRequestError",
                },
            );

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        const { from, to, interval } = parsedQuery.filter;

        if (utils.isNil(from)) {
            Logger.error("order retention rate: 'from' is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
        if (utils.isNil(to)) {
            Logger.error("order retention rate: 'to' is not sent", {
                userId,
                statusCode: 400,
                error: "badRequestError",
            });

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
        if (!INTERVALS.includes(interval)) {
            Logger.error(
                "order retention rate: 'interval' is not a part of INTERVALS is not sent",
                {
                    userId,
                    statusCode: 400,
                    error: "badRequestError",
                },
            );

            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }

        const data = await this._repo.getRetentionRate(
            userId,
            from,
            to,
            interval,
        );

        return {
            data,
            metadata: {
                from,
                to,
                interval,
            },
        };
    };
}
