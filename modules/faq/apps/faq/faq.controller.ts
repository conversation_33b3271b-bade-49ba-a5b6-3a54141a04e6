import { injectable } from "tsyringe";
import { OpenAPI } from "../../../common/lib/decorators";
import { Request, Response } from "express";

import FAQService from "./faq.service";
import { AddFAQSchema, EditFAQSchema } from "./schemas";
import { GetFAQResponseSchema, GetFAQsResponseSchema } from "./responses";

@injectable()
export default class FAQController {
    constructor(private _service: FAQService) {}

    @OpenAPI(
        "faqs",
        "/",
        "post",
        AddFAQSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addFAQ = async (req: Request, res: Response) => {
        const { body, user } = req;
        await this._service.addFAQ(body, user!);
        res.success({});
    };

    @OpenAPI(
        "faqs",
        "/",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "pageSize",
                schema: {
                    type: "number",
                    example: "10",
                },
            },
            {
                in: "query",
                name: "page",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
            {
                in: "query",
                name: "isActive",
                schema: {
                    type: "boolean",
                    example: "true",
                },
            },
            {
                in: "query",
                name: "search",
                schema: {
                    type: "string",
                    example: "password reset",
                },
            },
        ],
        GetFAQsResponseSchema,
        "bearerAuth",
    )
    getFAQs = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const faqs = await this._service.getFAQs(user!, parsedQuery);

        res.success(faqs);
    };

    @OpenAPI(
        "faqs",
        "/{id}",
        "get",
        undefined,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        GetFAQResponseSchema,
        "bearerAuth",
    )
    getFAQ = async (req: Request, res: Response) => {
        const {
            params: { id },
            user,
        } = req;
        const faq = await this._service.getFAQ(Number(id), user!);
        res.success(faq);
    };

    @OpenAPI(
        "faqs",
        "/{id}",
        "put",
        EditFAQSchema,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editFAQ = async (req: Request, res: Response) => {
        const {
            params: { id },
            body,
            user,
        } = req;
        await this._service.editFAQ(body, parseInt(id), user!);
        res.success({});
    };

    @OpenAPI(
        "faqs",
        "/{id}",
        "delete",
        undefined,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    deleteFAQ = async (req: Request, res: Response) => {
        const {
            params: { id },
            user,
        } = req;
        await this._service.deleteFAQ(Number(id), user!);
        res.success({});
    };
}
