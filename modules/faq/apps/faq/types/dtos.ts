import { FAQ } from "../types";

export interface AddFAQDto
    extends Pick<FAQ, "question" | "answer" | "status" | "order"> {}

export interface EditFAQDto
    extends Pick<FAQ, "question" | "answer" | "status" | "order"> {}

export interface FAQResponse
    extends Pick<FAQ, "id" | "question" | "answer" | "status" | "order"> {}

export interface GetFAQsFilterDto {
    pageSize?: number;
    page?: number;
    isActive?: boolean;
    search?: string;
}
