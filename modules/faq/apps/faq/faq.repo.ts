import { layers } from "../../../common";
import { FAQSchema } from "./faq.model";
import { FAQ, GetFAQsFilterDto } from "./types";

export default class FaqRepo extends layers.BaseTypeormRepository<FAQ> {
    relations = [];
    constructor() {
        super(FAQSchema);
    }

    getFAQs(userId: number, filters?: GetFAQsFilterDto) {
        let query = this.createQueryBuilder("faq")
            .where("faq.userId = :userId", { userId })
            .orderBy("faq.createdAt", "DESC");

        if (filters?.isActive !== undefined) {
            query = query.andWhere("faq.status = :status", { status: filters.isActive });
        }

        if (filters?.search) {
            query = query.andWhere(
                "(LOWER(faq.question) LIKE LOWER(:search) OR LOWER(faq.answer) LIKE LOWER(:search))",
                { search: `%${filters.search}%` }
            );
        }

        const page = filters?.page ?? 1;
        const pageSize = filters?.pageSize ?? 30;
        
        // Add pagination info but don't limit or offset here
        // The serializer will handle the actual pagination
        query = query.paginate(page, pageSize);

        return query.getRawManyAndCount();
    }

    getFAQ(faqId: number, userId: number) {
        return this.createQueryBuilder("faq")
            .where("faq.id = :faqId", { faqId })
            .andWhere("faq.userId = :userId", { userId })
            .getOne();
    }
}
