import { injectable } from "tsyringe";
import { addTokenDto } from "./types/dtos";
import NotificationConfigRepo from "./notifications-config.repo";

@injectable()
export default class NotificationConfigService {
    constructor(private _repo: NotificationConfigRepo) {}

    addToken = async (args: addTokenDto, profile: Express.User) => {
        const { id: userId } = profile;
        const { token } = args;

        // Check if token already exists for this user
        const existingToken = await this._repo.findByQuery({
            userId,
            token,
            type: "FCM",
        });

        if (existingToken.length > 0) {
            // Token already exists for this user, don't create duplicate
            return existingToken[0];
        }

        // Create new token entry
        return await this._repo.create({
            ...args,
            userId,
            type: "FCM",
        });
    };

    getUserTokens = async (userId: number) => {
        return this._repo.findByQuery({ userId });
    };
}
