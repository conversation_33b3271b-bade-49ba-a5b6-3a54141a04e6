import { JSONSchemaType } from "ajv";

export interface GetCurrentUserSubscriptionResponse {
    plan: {
        id: number;
        name: string;
        inventoryLimit: number;
        botLimit: number;
    };
    currentUsage: {
        inventoryUsage: number;
        botUsage: number;
    };
    subscription: {
        id: number;
        status: string;
        startDate: string;
        endDate: string;
        remainingDays: number;
    };
}

export const GetCurrentUserSubscriptionResponseSchema: JSONSchemaType<GetCurrentUserSubscriptionResponse> = {
    type: "object",
    properties: {
        plan: {
            type: "object",
            properties: {
                id: {
                    type: "number",
                },
                name: {
                    type: "string",
                },
                inventoryLimit: {
                    type: "number",
                },
                botLimit: {
                    type: "number",
                },
            },
            required: ["id", "name", "inventoryLimit", "botLimit"],
        },
        currentUsage: {
            type: "object",
            properties: {
                inventoryUsage: {
                    type: "number",
                },
                botUsage: {
                    type: "number",
                },
            },
            required: ["inventoryUsage", "botUsage"],
        },
        subscription: {
            type: "object",
            properties: {
                id: {
                    type: "number",
                },
                status: {
                    type: "string",
                },
                startDate: {
                    type: "string",
                    format: "date-time",
                },
                endDate: {
                    type: "string", 
                    format: "date-time",
                },
                remainingDays: {
                    type: "number",
                },
            },
            required: ["id", "status", "startDate", "endDate", "remainingDays"],
        },
    },
    required: ["plan", "currentUsage", "subscription"],
};
