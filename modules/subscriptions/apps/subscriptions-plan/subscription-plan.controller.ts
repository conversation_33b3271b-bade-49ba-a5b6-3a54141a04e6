import { injectable } from "tsyringe";
import SubscriptionPlanService from "./subscription-plan.service";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import { GetSubscriptionPlansResponseSchema } from "./responses";
import { AddSubscriptionPlanSchema } from "./schemas";

@injectable()
export default class SubscriptionPlanController {
    constructor(private _service: SubscriptionPlanService) {}

    @OpenAPI(
        "subscriptions/plans",
        "/",
        "post",
        AddSubscriptionPlanSchema,
        undefined,
        GetSubscriptionPlansResponseSchema,
        "bearerAuth",
    )
    addSubscriptionPlan = async (req: Request, res: Response) => {
        const { body } = req;

        await this._service.addSubscriptionPlan(body);

        res.success({});
    };

    @OpenAPI(
        "subscriptions/plans",
        "/",
        "get",
        undefined,
        undefined,
        GetSubscriptionPlansResponseSchema,
        "bearerAuth",
    )
    getSubscriptionPlans = async (req: Request, res: Response) => {
        const { parsedQuery } = req;

        const subscriptionPlans =
            await this._service.getSubscriptionPlans(parsedQuery);

        res.success(subscriptionPlans);
    };

    @OpenAPI(
        "subscriptions/plans",
        "/current",
        "get",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    getCurrentUserSubscription = async (req: Request, res: Response) => {
        const { user } = req;

        const currentSubscription = await this._service.getCurrentUserSubscription(user!.id);

        res.success(currentSubscription);
    };
}
