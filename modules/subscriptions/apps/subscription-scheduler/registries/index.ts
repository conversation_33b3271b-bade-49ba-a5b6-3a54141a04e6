import { createBullMQModule } from "../../../../common/lib/bullmq";
import { createRedisConnection } from "../../../../common/lib/redis";
import { createSmsService } from "../../../../common/lib/sms";

export const registries = [
    { token: "redis", useFactory: () => createRedisConnection },
    { token: "queue", useFactory: () => createBullMQModule },
    { token: "sms", useFactory: () => createSmsService },
];
