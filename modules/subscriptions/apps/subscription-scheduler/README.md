# Subscription Scheduler Service

This service provides automated job scheduling for subscription management, including expired subscription checks and expiry notifications.

## Features

### 1. Expired Subscriptions Check
- **Purpose**: Automatically updates subscription status from "EXECUTED" to "EXPIRED" for subscriptions that have passed their end date
- **Schedule**: Runs every hour
- **Process**: 
  - Finds all subscriptions with status "EXECUTED" that have `endDate < NOW()`
  - Updates their status to "EXPIRED"
  - Logs the changes for audit purposes

### 2. Expiry Notification Check
- **Purpose**: Sends SMS notifications to users whose subscriptions expire in exactly 3 days
- **Schedule**: Runs every 6 hours
- **Process**:
  - Finds all active subscriptions ("EXECUTED" status) that expire in exactly 3 days
  - Sends SMS notifications to users via Kavenegar service
  - Uses Redis to track notifications (prevents duplicate SMS for the same subscription)
  - Supports both English and Persian languages based on `PUBLIC_APP_LANG` environment variable

## SMS Template Setup

You need to create a template in your Kavenegar panel with the name `subscriptionExpiry` that accepts these tokens:
- `token`: User's first name
- `token2`: Subscription plan name  
- `token3`: Expiry date (formatted)

Example template (Persian):
```
کاربر گرامی {{token}}، اشتراک {{token2}} شما در تاریخ {{token3}} (3 روز دیگر) به پایان می‌رسد. لطفاً برای ادامه استفاده از خدمات، اشتراک خود را تمدید کنید.
```

Example template (English):
```
Dear {{token}}, your {{token2}} subscription will expire in 3 days ({{token3}}). Please renew to continue using our services.
```

## Manual Triggers

For testing and administrative purposes, you can manually trigger the jobs via API endpoints:

### Trigger Expired Subscriptions Check
```http
POST /api/v1/subscriptions/scheduler/trigger/expired-check
Authorization: Bearer <token>
```

### Trigger Expiry Notification Check  
```http
POST /api/v1/subscriptions/scheduler/trigger/expiry-notification
Authorization: Bearer <token>
```

## Redis Keys

The service uses Redis to track notifications and prevent duplicates:

- **Expiry Notifications**: `palette:subscription:{subscriptionId}:expiry_3day_notif`
  - TTL: 7 days
  - Purpose: Prevents sending multiple SMS for the same subscription expiry

## Environment Variables

- `PUBLIC_APP_LANG`: Controls SMS language ("en" for English, "fa" for Persian)
- `KAVENEGAR_API_KEY`: Required for SMS functionality
- `REDIS_PREFIX`: Redis key prefix (default: "palette")

## Database Requirements

The service requires these new methods in `UserSubscriptionRepo`:

### `findExpiredSubscriptions()`
Finds subscriptions that are "EXECUTED" but have passed their end date.

### `findSubscriptionsExpiringInDays(days: number)`
Finds subscriptions that are "EXECUTED" and expire in exactly N days, including user and plan data.

## Logging

All operations are logged with appropriate context including:
- Subscription IDs
- User IDs  
- Phone numbers (for SMS operations)
- Error details
- Execution counts

## Error Handling

- Failed SMS sends are logged but don't stop processing other subscriptions
- Database errors are caught and logged
- Redis connection issues are handled gracefully
- Individual subscription processing failures don't affect the batch

## Initialization

The scheduler is automatically initialized when the server starts. See `modules/server/index.ts`:

```typescript
await subscriptionSchedulerService.initializeScheduledJobs();
```

## Service Dependencies

- `UserSubscriptionRepo`: Database operations
- `KavenegarService`: SMS sending
- `BullMQ`: Job queue management  
- `Redis`: Notification tracking
- `Logger`: Structured logging
