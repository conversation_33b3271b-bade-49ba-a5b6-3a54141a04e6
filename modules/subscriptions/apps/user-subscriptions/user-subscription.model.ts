import { EntitySchema } from "typeorm";
import { UserSubscription } from "./types/entities";

export const UserSubscriptionSchema = new EntitySchema<UserSubscription>({
    name: "subscription",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        planId: {
            type: Number,
        },
        trackId: {
            type: String,
            nullable: true,
        },
        userId: {
            type: Number,
        },
        description: {
            type: String,
            nullable: true,
        },
        status: {
            type: String,
            enum: [
                "PENDING",
                "PAYED_ACCEPTED",
                "PAYED_NOT_ACCEPTED",
                "REJECTED",
                "EXECUTED",
                "CANCELLED",
                "EXPIRED",
            ],
            default: "PENDING",
        },
        rejectionReason: {
            type: String,
            enum: [
                "INTERNAL_ERROR",
                "USER_CANCEL",
                "INVALID_CARD_NUMBER",
                "INSUFFICIENT_BALANCE",
                "INVALID_PASSWORD",
                "REQUESTS_EXCEEDED",
                "DAILY_PAYMENT_EXCEEDED",
                "DAILY_PAYMENT_AMOUNT_EXCEEDED",
                "INVALID_PUBLISHER",
                "SWITCH_ERROR",
                "NOT_ACCESSIBLE_CARD",
            ],
            nullable: true,
        },
        startDate: {
            type: Date,
        },
        endDate: {
            type: Date,
        },
    },
    relations: {
        plan: {
            type: "many-to-one",
            target: "subscription-plan",
        },
        user: {
            type: "many-to-one",
            target: "user",
        },
    },
});
