import { injectable } from "tsyringe";
import UserSubscriptionService from "./user-subscription.service";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import { AddUserSubscriptionSchema } from "./schemas";
import {
    CreatePaymentResponseSchema,
    GetUserSubscriptionsResponseSchema,
} from "./responses";
import { ZibalCallBackQueryDto } from "./types";
@injectable()
export default class UserSubscriptionController {
    constructor(private _service: UserSubscriptionService) {}

    @OpenAPI(
        "user-subscription",
        "/",
        "post",
        AddUserSubscriptionSchema,
        undefined,
        CreatePaymentResponseSchema,
        "bearerAuth",
    )
    addUserSubscription = async (req: Request, res: Response) => {
        const { body, user } = req;

        const payment = await this._service.addUserSubscription(body, user!);

        res.success(payment);
    };

    @OpenAPI(
        "user-subscription",
        "/",
        "get",
        undefined,
        undefined,
        GetUserSubscriptionsResponseSchema,
        "bearerAuth",
    )
    getSubscriptionsOfUser = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const userSubscription = await this._service.getSubscriptionsOfUser(
            user!,
            parsedQuery,
        );

        res.success(userSubscription);
    };

    verifyPaymentWebhook = async (req: Request, res: Response) => {
        const { query } = req;

        console.log("query from verifyPaymentWebhook ",query);

        const redirectUrl = await this._service.verifyPayment(
            query as unknown as ZibalCallBackQueryDto,
        );

        console.log("redirectUrl", redirectUrl)
        res.redirect(redirectUrl);
    };
}
