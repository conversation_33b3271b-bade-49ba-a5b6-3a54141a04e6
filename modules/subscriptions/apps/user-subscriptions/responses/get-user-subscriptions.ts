import { JSONSchemaType } from "ajv";
import { GetUserSubscriptionResponse } from "../types";

export const GetUserSubscriptionsResponseSchema: JSONSchemaType<
    GetUserSubscriptionResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "subscription_",
        properties: {
            id: {
                type: "number",
            },
            trackId: {
                type: "string",
            },
            status: {
                type: "string",
            },
            endDate: {
                type: "string",
                format: "date-time",
            },
            startDate: {
                type: "string",
                format: "date-time",
            },
            plan: {
                type: "object",
                prefix: "subscription-plan_",
                properties: {
                    id: {
                        type: "number",
                    },
                    name: {
                        type: "string",
                    },
                    price: {
                        type: "string",
                    },
                    isPopular: {
                        type: "boolean",
                    },
                    duration: {
                        type: "integer",
                    },
                },
                required: [],
            },
        },
        required: [],
    },
};
