import { User } from "../../../../common/lib/instagram";
import { SubscriptionPlan } from "../../subscriptions-plan/types";

export interface UserSubscription {
    id: number;
    plan: SubscriptionPlan;
    planId: number;
    trackId: string;
    user: User;
    userId: number;
    description?: string;
    status:
        | "PENDING"
        | "PAYED_ACCEPTED"
        | "PAYED_NOT_ACCEPTED"
        | "REJECTED"
        | "EXECUTED"
        | "EXPIRED"
        | "CANCELLED";
    rejectionReason?:
        | "INTERNAL_ERROR"
        | "USER_CANCEL"
        | "INVALID_CARD_NUMBER"
        | "INSUFFICIENT_BALANCE"
        | "INVALID_PASSWORD"
        | "REQUESTS_EXCEEDED"
        | "DAILY_PAYMENT_EXCEEDED"
        | "DAILY_PAYMENT_AMOUNT_EXCEEDED"
        | "INVALID_PUBLISHER"
        | "SWITCH_ERROR"
        | "NOT_ACCESSIBLE_CARD";
    startDate: Date;
    endDate: Date;
}
