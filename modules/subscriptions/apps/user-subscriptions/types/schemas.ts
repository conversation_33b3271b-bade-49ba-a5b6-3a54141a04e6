export interface GetUserSubscriptionResponse {
    id: number;
    status: string;
    endDate: string;
    startDate: string;
    plan: {
        id: number;
        name: string;
        price: string;
        isPopular: boolean;
        duration: number;
    };
}

// Interface for raw database row from getCurrentSubscriptionOfUser
export interface CurrentSubscriptionRow {
    id: number;
    status: string;
    startDate: Date;
    endDate: Date;
    plan: {
        id: number;
        name: string;
        price: string;
        isPopular: boolean;
        duration: number;
    };
}

export interface CreatePaymentResponse {
    trackId: string;
    payLink: string;
}
