import { layers } from "../../../common";
import { UserSubscriptionSchema } from "./user-subscription.model";
import { UserSubscription } from "./types";
import { FindOptionsWhere } from "typeorm";

export default class UserSubscriptionRepo extends layers.BaseTypeormRepository<UserSubscription> {
    relations = [];
    constructor() {
        super(UserSubscriptionSchema);
    }

    getUserSubscriptionHistory = async (
        userId: number,
        query: Partial<Express.Query>,
    ) => {
        return this._repo
            .createQueryBuilder("subscription")
            .where("subscription.userId = :userId", { userId })
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getLatestSubscriptionOfUser = async (userId: number) => {
        return this._repo
            .createQueryBuilder("subscription")
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription.status = 'EXECUTED'")
            .orderBy("subscription.endDate", "DESC")
            .addOrderBy("subscription.id", "DESC")
            .getOne();
    };

    getNearestSubscriptionOfUser = async (userId: number) => {
        return this._repo
            .createQueryBuilder("subscription")
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription.status = 'EXECUTED'")
            .andWhere("subscription.endDate > NOW()")
            .orderBy("subscription.endDate", "ASC")
            .getRawOne();
    };

    getDepositByTrackId = async (trackId: string) => {
        return this.findOneByQuery({ trackId });
    };

    getActiveSubscriptionByUserAndPlan = async (
        userId: number,
        planId: number,
    ) => {
        return this._repo
            .createQueryBuilder("subscription")
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription.planId = :planId", { planId })
            .andWhere("subscription.status IN (:...statuses)", {
                statuses: ["PAYED_ACCEPTED", "EXECUTED"],
            })
            .andWhere("subscription.endDate > NOW()")
            .getOne();
    };

    hasUserEverHadFreeSubscription = async (userId: number) => {
        const result = await this._repo
            .createQueryBuilder("subscription")
            .leftJoin(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription-plan.price = '0'")
            .andWhere("subscription.status IN (:...statuses)", {
                statuses: ["EXECUTED", "EXPIRED", "CANCELLED"],
            })
            .getOne();

        return result !== null;
    };

    hasUserAnyPaidSubscription = async (userId: number) => {
        const result = await this._repo
            .createQueryBuilder("subscription")
            .leftJoin(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription-plan.price != '0'")
            .andWhere("subscription.status IN (:...statuses)", {
                statuses: [
                    "PAYED_ACCEPTED",
                    "EXECUTED",
                    "EXPIRED",
                    "CANCELLED",
                ],
            })
            .getOne();

        return result !== null;
    };

    /**
     * Find all subscriptions that are EXECUTED but have passed their end date
     */
    findExpiredSubscriptions = async () => {
        return this._repo
            .createQueryBuilder("subscription")
            .where("subscription.status = 'EXECUTED'")
            .andWhere("subscription.endDate < NOW()")
            .getMany();
    };

    /**
     * Find subscriptions that are EXECUTED and expire in exactly N days
     */
    findSubscriptionsExpiringInDays = async (days: number) => {
        return this._repo
            .createQueryBuilder("subscription")
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .leftJoinAndSelect("user", "user", "user.id = subscription.userId")
            .where("subscription.status = 'EXECUTED'")
            .andWhere("subscription.endDate > NOW()")
            .andWhere(
                "DATE(subscription.endDate) = DATE(NOW() + INTERVAL :days DAY)",
                { days },
            )
            .getRawMany();
    };

    /**
     * Find subscriptions by conditions
     * This method delegates to the base findByQuery method
     */
    findByConditions = async (
        conditions: FindOptionsWhere<UserSubscription>,
    ) => {
        return this.findByQuery(conditions);
    };
}
