// Redis key prefixes and patterns for subscription services
const REDIS_KEY_PREFIX = "palette";

// Shared Redis key creation functions
export const SUBSCRIPTION_REDIS_KEYS = {
    // Usage tracking keys (per subscription, cycle, and feature)
    USAGE: (subscriptionId: number, feature: string, cycle: number) => 
        `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:cycle:${cycle}:${feature.toLowerCase()}_usage`,
    
    // Notification keys (per subscription and cycle for feature limits)
    NOTIFICATION: (subscriptionId: number, cycle: number, notificationType: string) =>
        `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:cycle:${cycle}:${notificationType}`,
    
    // Expiry notification keys (per subscription, no cycle)
    EXPIRY_NOTIFICATION: (subscriptionId: number) => 
        `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:expiry_3day_notif`,
    
    // Pattern for cleanup (all keys for a subscription)
    SUBSCRIPTION_PATTERN: (subscriptionId: number) => 
        `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:*`,
};

// TTL constants
export const SUBSCRIPTION_TTL = {
    NOTIFICATION: 30 * 24 * 60 * 60, // 30 days for feature notifications
    EXPIRY_NOTIFICATION: 7 * 24 * 60 * 60, // 7 days for expiry notifications
};