export * from "./entities";

// DTOs
export interface CreateTransactionDto {
    userId: number;
    type: TransactionType;
    amount: string;
    currency: Currency;
    paymentMethod: PaymentMethod;
    subscriptionPlanId?: number;
    trackId?: string;
    description?: string;
    meta?: Record<string, unknown>;
}

export interface UpdateTransactionDto {
    status?: TransactionStatus;
    trackId?: string;
    description?: string;
    meta?: Record<string, unknown>;
}

// Response Types
export interface GetTransactionResponse {
    id: number;
    type: TransactionType;
    status: TransactionStatus;
    amount: string;
    currency: Currency;
    paymentMethod: PaymentMethod;
    trackId?: string;
    description?: string;
    createdAt: string;
    updatedAt: string;
    user?: {
        id: number;
        firstName: string;
        lastName: string;
        email: string;
    };
    subscriptionPlan?: {
        id: number;
        name: string;
        price: string;
    };
}

// Import the enums and interface from entities
import {
    Currency,
    PaymentMethod,
    TransactionStatus,
    TransactionType,
} from "./entities";
