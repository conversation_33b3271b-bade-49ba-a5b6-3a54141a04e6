export enum TransactionType {
    SUBSCRIPTION = "SUBSCRIPTION",
    CHARGE_BOT = "CHARGE_BOT",
}

export enum TransactionStatus {
    PENDING = "PENDING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    REFUNDED = "REFUNDED",
}

export enum PaymentMethod {
    ZIBAL = "ZIBAL",
    CARD = "CARD",
    WALLET = "WALLET",
    BANK_TRANSFER = "BANK_TRANSFER",
}

export enum Currency {
    IRR = "IRR",
    USD = "USD",
}

export interface Transaction {
    id: number;
    userId: number;
    type: TransactionType;
    status: TransactionStatus;
    amount: string; // Using string for precision with large numbers
    currency: Currency;
    paymentMethod: PaymentMethod;
    trackId?: string; // Payment gateway tracking ID
    subscriptionPlanId?: number; // Nullable - only for SUBSCRIPTION type
    description?: string;
    meta?: Record<string, unknown>; // JSON field for additional data
    createdAt: Date;
    updatedAt: Date;

    // Relations
    user?: unknown; // Will be populated when relations are loaded
    subscriptionPlan?: unknown; // Will be populated when relations are loaded
}
