import { layers } from "../../../common";
import { TransactionSchema } from "./transaction.model";
import {
    Transaction,
    TransactionStatus,
    TransactionType,
} from "./types/entities";

export default class TransactionRepo extends layers.BaseTypeormRepository<Transaction> {
    relations = ["user", "subscriptionPlan"];

    constructor() {
        super(TransactionSchema);
    }

    getTransactionHistory = async (
        userId: number,
        query: Partial<Express.Query>,
    ) => {
        return this._repo
            .createQueryBuilder("transaction")
            .where("transaction.userId = :userId", { userId })
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = transaction.subscriptionPlanId",
            )
            .leftJoinAndSelect("user", "user", "user.id = transaction.userId")
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getTransactionByTrackId = async (trackId: string) => {
        return this.findOneByQuery({ trackId });
    };

    getTransactionsByType = async (
        type: TransactionType,
        query: Partial<Express.Query>,
    ) => {
        return this._repo
            .createQueryBuilder("transaction")
            .where("transaction.type = :type", { type })
            .leftJoinAndSelect("user", "user", "user.id = transaction.userId")
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = transaction.subscriptionPlanId",
            )
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getTransactionsByStatus = async (
        status: TransactionStatus,
        query: Partial<Express.Query>,
    ) => {
        return this._repo
            .createQueryBuilder("transaction")
            .where("transaction.status = :status", { status })
            .leftJoinAndSelect("user", "user", "user.id = transaction.userId")
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = transaction.subscriptionPlanId",
            )
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getRecentTransactions = async (userId: number, limit: number = 10) => {
        return this._repo
            .createQueryBuilder("transaction")
            .where("transaction.userId = :userId", { userId })
            .orderBy("transaction.createdAt", "DESC")
            .limit(limit)
            .getMany();
    };

    getTotalAmountByUser = async (
        userId: number,
        type?: TransactionType,
    ): Promise<number> => {
        let query = this._repo
            .createQueryBuilder("transaction")
            .select("SUM(CAST(transaction.amount AS DECIMAL))", "total")
            .where("transaction.userId = :userId", { userId })
            .andWhere("transaction.status = :status", { status: "COMPLETED" });

        if (type !== null) {
            query = query.andWhere("transaction.type = :type", { type });
        }

        const result = (await query.getRawOne()) as { total: string };
        return parseFloat(result?.total ?? "0") || 0;
    };
}
