import { EntitySchema } from "typeorm";
import { Transaction } from "./types/entities";

export const TransactionSchema = new EntitySchema<Transaction>({
    name: "transaction",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        userId: {
            type: Number,
        },
        type: {
            type: String,
            enum: ["SUBSCRIPTION", "CHARGE_BOT"],
        },
        status: {
            type: String,
            enum: ["PENDING", "COMPLETED", "FAILED", "CANCELLED", "REFUNDED"],
            default: "PENDING",
        },
        amount: {
            type: "numeric",
            precision: 15,
            scale: 2,
        },
        currency: {
            type: String,
            enum: ["IRR", "USD"],
            default: "IRR",
        },
        paymentMethod: {
            type: String,
            enum: ["ZIBAL", "CARD", "WALLET", "BANK_TRANSFER"],
        },
        trackId: {
            type: String,
            nullable: true,
        },
        subscriptionPlanId: {
            type: Number,
            nullable: true,
        },
        description: {
            type: String,
            nullable: true,
        },
        meta: {
            type: "json",
            nullable: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
            joinColumn: {
                name: "userId",
            },
        },
        subscriptionPlan: {
            type: "many-to-one",
            target: "subscription-plan",
            joinColumn: {
                name: "subscriptionPlanId",
            },
            nullable: true,
        },
    },
});
