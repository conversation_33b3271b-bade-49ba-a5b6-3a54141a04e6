import { injectable, registry } from "tsyringe";
import TransactionRepo from "./transaction.repo";
import {
    CreateTransactionDto,
    Currency,
    PaymentMethod,
    Transaction,
    TransactionStatus,
    TransactionType,
    UpdateTransactionDto,
} from "./types";
import { errors, utils } from "../../../common";
import { getTransactionsSerializer } from "./responses";
import Logger from "../../../common/lib/metrics/logger";
import { registries } from "./registries";

@registry(registries)
@injectable()
export default class TransactionService {
    constructor(private _repo: TransactionRepo) {}

    createTransaction = async (
        args: CreateTransactionDto,
    ): Promise<Transaction> => {
        const transaction = await this._repo.create(args);

        Logger.info("Transaction created", {
            transactionId: transaction.id,
            userId: args.userId,
            type: args.type,
            amount: args.amount,
        });

        return transaction;
    };

    updateTransaction = async (
        transactionId: number,
        args: UpdateTransactionDto,
    ): Promise<void> => {
        const transaction = await this._repo.findById(transactionId);

        if (utils.isNil(transaction)) {
            throw new errors.NotFoundError("Transaction");
        }

        await this._repo.updateById(transactionId, args);

        Logger.info("Transaction updated", {
            transactionId,
            updates: args,
        });
    };

    getTransactionById = async (
        transactionId: number,
    ): Promise<Transaction | null> => {
        return await this._repo.findById(transactionId);
    };

    getTransactionByTrackId = async (
        trackId: string,
    ): Promise<Transaction | null> => {
        return await this._repo.getTransactionByTrackId(trackId);
    };

    getUserTransactions = async (
        userId: number,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const transactions = await this._repo.getTransactionHistory(
            userId,
            parsedQuery,
        );

        return getTransactionsSerializer(transactions);
    };

    getTransactionsByType = async (
        type: TransactionType,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const transactions = await this._repo.getTransactionsByType(
            type,
            parsedQuery,
        );

        return getTransactionsSerializer(transactions);
    };

    getTransactionsByStatus = async (
        status: TransactionStatus,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const transactions = await this._repo.getTransactionsByStatus(
            status,
            parsedQuery,
        );

        return getTransactionsSerializer(transactions);
    };

    getRecentTransactions = async (
        userId: number,
        limit: number = 10,
    ): Promise<Transaction[]> => {
        return await this._repo.getRecentTransactions(userId, limit);
    };

    getUserTotalSpent = async (
        userId: number,
        type?: TransactionType,
    ): Promise<number> => {
        return await this._repo.getTotalAmountByUser(userId, type);
    };

    markTransactionAsCompleted = async (
        transactionId: number,
    ): Promise<void> => {
        await this.updateTransaction(transactionId, {
            status: TransactionStatus.COMPLETED,
        });
    };

    markTransactionAsFailed = async (
        transactionId: number,
        reason?: string,
    ): Promise<void> => {
        const updateData: UpdateTransactionDto = {
            status: TransactionStatus.FAILED,
        };

        if (utils.isNotNil(reason)) {
            updateData.description = reason;
        }

        await this.updateTransaction(transactionId, updateData);
    };

    // Helper method to create subscription transaction
    createSubscriptionTransaction = async (
        userId: number,
        subscriptionPlanId: number,
        amount: string,
        paymentMethod: PaymentMethod,
        trackId?: string,
        meta?: Record<string, unknown>,
    ): Promise<Transaction> => {
        return await this.createTransaction({
            userId,
            type: TransactionType.SUBSCRIPTION,
            amount,
            currency: Currency.IRR,
            paymentMethod,
            subscriptionPlanId,
            trackId,
            meta,
        });
    };

    // Helper method to create bot charge transaction
    createBotChargeTransaction = async (
        userId: number,
        amount: string,
        paymentMethod: PaymentMethod,
        trackId?: string,
        meta?: Record<string, unknown>,
    ): Promise<Transaction> => {
        return await this.createTransaction({
            userId,
            type: TransactionType.CHARGE_BOT,
            amount,
            currency: Currency.IRR,
            paymentMethod,
            trackId,
            meta,
        });
    };
}
