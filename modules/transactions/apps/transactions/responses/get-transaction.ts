import { JSONSchemaType } from "ajv";
import { GetTransactionResponse } from "../types";

export const GetTransactionResponseSchema: JSONSchemaType<GetTransactionResponse> =
    {
        type: "object",
        prefix: "transaction_",
        properties: {
            id: {
                type: "number",
            },
            type: {
                type: "string",
            },
            status: {
                type: "string",
            },
            amount: {
                type: "string",
            },
            currency: {
                type: "string",
            },
            paymentMethod: {
                type: "string",
            },
            trackId: {
                type: "string",
                nullable: true,
            },
            description: {
                type: "string",
                nullable: true,
            },
            createdAt: {
                type: "string",
                format: "date-time",
            },
            updatedAt: {
                type: "string",
                format: "date-time",
            },
            user: {
                type: "object",
                prefix: "user_",
                nullable: true,
                properties: {
                    id: {
                        type: "number",
                    },
                    firstName: {
                        type: "string",
                    },
                    lastName: {
                        type: "string",
                    },
                    email: {
                        type: "string",
                    },
                },
                required: [],
            },
            subscriptionPlan: {
                type: "object",
                prefix: "subscription-plan_",
                nullable: true,
                properties: {
                    id: {
                        type: "number",
                    },
                    name: {
                        type: "string",
                    },
                    price: {
                        type: "string",
                    },
                },
                required: [],
            },
        },
        required: [
            "id",
            "type",
            "status",
            "amount",
            "currency",
            "paymentMethod",
            "createdAt",
            "updatedAt",
        ],
    };
