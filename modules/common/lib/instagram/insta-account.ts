import { errors } from "..";
import {
    INSTA_GRAPH_API,
    InstagramProfileResponse,
    InstagramSendInventoryDto,
    InstagramSuccessResponse,
} from "../../base/types/instagram.type";
import Logger from "../metrics/logger";
import {
    getProfileFailedToFetch,
    getProfileTotal,
    recordErrorValue,
    subscriptionToEventsFailure,
} from "../metrics/metrics";

export class AccountService {
    private _accessToken: string;
    private _profileData = ["user_id", "username", "name"];
    private _testMode: boolean;

    constructor(accessToken: string, testMode: boolean = false) {
        this._accessToken = accessToken;
        this._testMode = testMode;
    }

    getProfile = async () => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would get Instagram profile", {
                action: "getProfile",
                testMode: true,
            });
            return {
                user_id: "test_user_id",
                username: "test_username",
                name: "Test User",
            } as InstagramProfileResponse;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me?fields=${this._profileData.join(",")}&access_token=${this._accessToken}`;
            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = (await response.json()) as InstagramProfileResponse;

            getProfileTotal.inc();
            Logger.info("get instagram profile of user", {
                action: "get profile",
                message: "successfully return profile of instagram user",
            });
            return data;
        } catch (error) {
            console.error(error);
            Logger.error("getting profile of instagram user is failed", {
                action: "getProfile of instagram user",
                error: "badRequestError",
            });

            getProfileFailedToFetch.inc();
            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
    };

    subscribeToEvents = async () => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would subscribe to Instagram events", {
                action: "subscribeToEvents",
                testMode: true,
            });
            return {
                success: true,
            } as InstagramSuccessResponse;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/subscribed_apps?subscribed_fields=messages&access_token=${this._accessToken}`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = (await response.json()) as InstagramSuccessResponse;

            Logger.info("subscribe to events successfully done", {
                action: "subscribe to events",
                message: "successfully subscribe to events",
            });
            return data;
        } catch (error) {
            console.error(error);
            Logger.error("subscribeToEvents failed", {
                action: "subscribe to events",
                error: "badRequestError",
            });

            subscriptionToEventsFailure.inc();
            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
    };

    getUserProfile = async (platformId: string) => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would get Instagram user profile", {
                action: "getUserProfile",
                platformId,
                testMode: true,
            });
            return {
                user_id: platformId,
                username: `test_user_${platformId}`,
                name: "Test User Profile",
            } as InstagramProfileResponse;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/${platformId}?fields=username&access_token=${this._accessToken}`;
            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = (await response.json()) as InstagramProfileResponse;
            return data;
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendMessage = async (
        to: string,
        text: string,
        useHumanAgentTag: boolean = false,
    ) => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would send Instagram message", {
                action: "sendMessage",
                to,
                text,
                useHumanAgentTag,
                testMode: true,
            });
            return;
        }

        const payload: {
            recipient: { id: string };
            message: { text: string };
            tag?: string;
        } = {
            recipient: {
                id: to,
            },
            message: {
                text,
            },
        };

        // Only include the tag if useHumanAgentTag is true
        if (useHumanAgentTag) {
            payload.tag = "HUMAN_AGENT"; // Include this tag when responding outside the 24-hour window
        }

        const response = await fetch(`${INSTA_GRAPH_API}/v20.0/me/messages`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${this._accessToken}`,
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            console.error(await response.text());
            throw new errors.BadRequestError();
        }
    };

    sendInventory = async (
        to: string,
        inventory: InstagramSendInventoryDto,
    ) => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would send Instagram inventory", {
                action: "sendInventory",
                to,
                inventory,
                testMode: true,
            });
            return;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: "image",
                            payload: {
                                url: inventory.image,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendImage = async (to: string, image: string) => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would send Instagram image", {
                action: "sendImage",
                to,
                image,
                testMode: true,
            });
            return;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: "image",
                            payload: {
                                url: image,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendVideo = async (to: string, video: string) => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would send Instagram video", {
                action: "sendVideo",
                to,
                video,
                testMode: true,
            });
            return;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: "video",
                            payload: {
                                url: video,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendAudio = async (to: string, audio: string) => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would send Instagram audio", {
                action: "sendAudio",
                to,
                audio,
                testMode: true,
            });
            return;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: "audio",
                            payload: {
                                url: audio,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendFile = async (to: string, fileUrl: string, fileType: "image" | "video" | "audio" | "file" = "file") => {
        if (this._testMode) {
            Logger.info("TEST MODE: Would send Instagram file", {
                action: "sendFile",
                to,
                fileUrl,
                fileType,
                testMode: true,
            });
            return;
        }

        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: fileType,
                            payload: {
                                url: fileUrl,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };
}
