import { singleton } from "tsyringe";
import pg from "pg";

@singleton()
export class PostgresPool {
    private _pool: pg.Pool;

    constructor() {
        this._pool = new pg.Pool({
            host: process.env.POSTGRES_DB_HOST,
            port: Number(process.env.POSTGRES_DB_PORT),
            user: process.env.POSTGRES_DB_USER,
            password: process.env.POSTGRES_DB_PASSWORD,
            database: process.env.POSTGRES_DB_NAME,
        });

        // Log when the pool is created
        console.log('PostgreSQL connection pool created successfully');

        // Handle pool errors
        this._pool.on('error', (err) => {
            console.error('Unexpected error on idle PostgreSQL client', err);
        });
    }

    /**
     * Get the underlying pool
     */
    get pool(): pg.Pool {
        return this._pool;
    }

    /**
     * Get a client connection from the pool
     * @returns A PostgreSQL client from the pool
     */
    async getConnection(): Promise<pg.PoolClient> {
        try {
            const client = await this._pool.connect();
            return client;
        } catch (error) {
            console.error('Error getting PostgreSQL connection:', error);
            throw error;
        }
    }
}
