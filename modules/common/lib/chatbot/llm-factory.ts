// import { Chat<PERSON><PERSON>exA<PERSON> } from "@langchain/google-vertexai";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { CHAT_MODEL, EMBEDDING_MODEL, MODEL_CONFIG } from "../../base/types/typing";

export function llmFactory(model: CHAT_MODEL) {
    if (model === CHAT_MODEL.OPENAI) {
        return new ChatOpenAI({
            modelName: MODEL_CONFIG.OPENAI_MODEL_GPT4O,
            temperature: 0.3,
            topP: 0.9,
            presencePenalty: 0.6,
            maxRetries: 5,
            timeout: 45000, // 45 seconds
        });
    }

    // if (model === CHAT_MODEL.VERTEX) {
    //     return new ChatVertexAI({
    //         model: MODEL_CONFIG.GEMINI_MODEL,
    //         temperature: 0.8,
    //         topP: 0.9,
    //     });
    // }

    throw new Error("Unsupported model name");
}

export function embeddingFactory(model: EMBEDDING_MODEL) {
    if (model === EMBEDDING_MODEL.OPENAI_EMBEDDING) {
        return new OpenAIEmbeddings({
            model: MODEL_CONFIG.OPENAI_EMBEDDING_MODEL,
        });
    }

    throw new Error("Unsupported model name");
}
