import { z } from "zod";
import { tool } from "@langchain/core/tools";
import { ClientOrderResponse } from "../../../orders/apps/orders/types/schemas";
import { OrderUtils } from "./utils/order-utils";
import { normalizeOrderResponse } from "../utils/text-normalizer";
import { AppLanguage } from "../../base/types/typing";

export const createGetOrderStatusTool = (
    fn: (userId: number) => Promise<ClientOrderResponse[]>,
    language: AppLanguage
) =>
    tool(
        async (input: { userId: number }) => {
            try {
                console.log(`Fetching orders for userId: ${input.userId}`);
                const result = await fn(input.userId);
                console.log(`Found ${result.length} orders`);

                // Limit to 5 most recent orders due to Instagram character limitations
                const totalOrderCount = result.length;
                const limitedOrders = result.slice(0, 5);
                console.log(`Limiting to ${limitedOrders.length} orders out of ${totalOrderCount} due to Instagram character limit`);

                // Format the orders using our utility function, passing the language and total count
                const formattedOrders = OrderUtils.formatOrdersList(limitedOrders, language, totalOrderCount);
                console.log('Formatted orders response:', formattedOrders.substring(0, 100) + '...');

                // Return the formatted string instead of the raw data
                return formattedOrders;
            } catch (error) {
                console.error('Error fetching orders:', error);
                return normalizeOrderResponse(
                    language === "en"
                        ? "Sorry, there was an error retrieving your orders. Please try again."
                        : "متاسفانه در دریافت اطلاعات سفارش‌ها مشکلی پیش آمد. لطفا مجددا تلاش کنید."
                );
            }
        },
        {
            name: "orderStatus",
            description: "Get information about the user's ORDERS (سفارش) - NOT cart (سبد خرید). Use this tool when users ask about their orders using words like 'سفارش', 'سفارشات', or 'سفارش‌های من'. NEVER use this tool for cart inquiries.",
            schema: z.object({
                userId: z.number().describe("User ID to identify the user"),
            }),
        },
    );
