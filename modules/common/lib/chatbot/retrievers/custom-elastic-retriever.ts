import { BaseRetriever } from "@langchain/core/retrievers";

const makeCustomElasticRetrieverTool = ({
    name,
    description,
    retriever,
    isProductAvailable
}: {
    retriever: BaseRetriever;
    name?: string;
    description?: string;
    isProductAvailable: (id:number)=>Promise<boolean>
}) => ({
    name,
    description,
    async invoke({query}:{
        query:string
    }) {
        const docs = await retriever.getRelevantDocuments(query);
        const results = await Promise.all(
            docs.map(async (doc) => {
            // Extract ID from pageContent using regex pattern (id: x)
            const idMatch = doc.pageContent.match(/\(id:\s*(\d+)\)/);
            const id = idMatch ? Number(idMatch[1]) : -1;
            const available = await isProductAvailable(id);
            return available ? doc.pageContent : null;
            })
        );
        return results.filter((content) => content !== null);
    },
});

export default makeCustomElasticRetrieverTool;
