import { BaseMessage, AIMessage, HumanMessage, SystemMessage } from "@langchain/core/messages";
import { MessageHistoryRepo } from "../repositories/message-history.repo";

// Define our own BaseMessageHistory class since the import path might be different in different LangChain versions
abstract class BaseMessageHistory {
  abstract getMessages(): Promise<BaseMessage[]>;
  abstract addUserMessage(message: string): Promise<void>;
  abstract addAIMessage(message: string): Promise<void>;
  abstract clear(): Promise<void>;
}

// Add a message cache TTL to avoid stale data
const MESSAGE_CACHE_TTL = 60 * 1000; // 1 minute in milliseconds

export class PGMessageHistory extends BaseMessageHistory {
  private _messages: BaseMessage[] = [];
  private _lastLoadTime: number = 0;
  private _isLoading: boolean = false;
  private _loadPromise: Promise<void> | null = null;

  constructor(
    private readonly threadId: string,
    private readonly messageHistoryRepo: MessageHistoryRepo
  ) {
    super();
    console.log(`[PGMessageHistory:constructor] Created with threadId: ${threadId}`);
  }

  async getMessages(): Promise<BaseMessage[]> {
    const now = Date.now();

    // If cache is stale or empty, load messages
    if (now - this._lastLoadTime > MESSAGE_CACHE_TTL || this._messages.length === 0) {
      await this.loadMessagesFromDB();
    }

    return this._messages;
  }

  async addUserMessage(message: string): Promise<void> {
    console.log(`[PGMessageHistory:addUserMessage] Adding user message for threadId: ${this.threadId}`);

    const userMessage = new HumanMessage(message);

    // Add to in-memory cache first
    this._messages.push(userMessage);

    try {
      await this.messageHistoryRepo.saveMessage({
        threadId: this.threadId,
        content: message,
        role: 'user'
      });
      console.log(`[PGMessageHistory:addUserMessage] User message saved for threadId: ${this.threadId}`);
    } catch (error) {
      console.error(`[PGMessageHistory:addUserMessage] Error saving user message:`, error);
      // Remove from cache if DB save failed
      this._messages.pop();
      throw error;
    }
  }

  async addSystemMessage(message: string): Promise<void> {
    console.log(`[PGMessageHistory:addSystemMessage] Adding system message for threadId: ${this.threadId}`);

    const systemMessage = new SystemMessage(message);

    // Add to in-memory cache first
    this._messages.push(systemMessage);

    try {
      await this.messageHistoryRepo.saveMessage({
        threadId: this.threadId,
        content: message,
        role: 'system'
      });
      console.log(`[PGMessageHistory:addSystemMessage] System message saved for threadId: ${this.threadId}`);
    } catch (error) {
      console.error(`[PGMessageHistory:addSystemMessage] Error saving system message:`, error);
      // Remove from cache if DB save failed
      this._messages.pop();
      throw error;
    }
  }

  async addAIMessage(message: string): Promise<void> {
    console.log(`[PGMessageHistory:addAIMessage] Adding AI message for threadId: ${this.threadId}`);

    const aiMessage = new AIMessage(message);

    // Add to in-memory cache first
    this._messages.push(aiMessage);

    try {
      await this.messageHistoryRepo.saveMessage({
        threadId: this.threadId,
        content: message,
        role: 'assistant'
      });
      console.log(`[PGMessageHistory:addAIMessage] AI message saved for threadId: ${this.threadId}`);
    } catch (error) {
      console.error(`[PGMessageHistory:addAIMessage] Error saving AI message:`, error);
      // Remove from cache if DB save failed
      this._messages.pop();
      throw error;
    }
  }

  async clear(): Promise<void> {
    console.log(`[PGMessageHistory:clear] Clearing messages for threadId: ${this.threadId}`);

    // Clear in-memory cache first
    this._messages = [];
    this._lastLoadTime = 0;

    try {
      await this.messageHistoryRepo.clearMessages(this.threadId);
      console.log(`[PGMessageHistory:clear] Messages cleared for threadId: ${this.threadId}`);
    } catch (error) {
      console.error(`[PGMessageHistory:clear] Error clearing messages:`, error);
      throw error;
    }
  }

  // Add a method to add context reminder messages
  async addContextReminder(contextInfo: string): Promise<void> {
    console.log(`[PGMessageHistory:addContextReminder] Adding context reminder for threadId: ${this.threadId}`);

    const reminderMessage = `[Context Reminder: ${contextInfo}]`;
    await this.addSystemMessage(reminderMessage);
  }

  /**
   * Configure message history limits
   * This allows dynamically adjusting how many messages are loaded from history
   *
   * @param config - Configuration options
   * @param config.maxMessages - Maximum number of messages to retrieve (default: 100)
   * @param config.hoursBack - Only retrieve messages from the last X hours (default: null = no time limit)
   *                          Set to null to retrieve all messages regardless of age
   */
  configureMessageLimits(config: { maxMessages?: number; hoursBack?: number | null }): void {
    if (config.maxMessages !== undefined && config.maxMessages > 0) {
      this._messageConfig.maxMessages = config.maxMessages;
    }

    if (config.hoursBack !== undefined) {
      // Allow null (no time limit) or positive numbers
      if (config.hoursBack === null || config.hoursBack > 0) {
        this._messageConfig.hoursBack = config.hoursBack;
      }
    }

    let logMessage = `[PGMessageHistory:configureMessageLimits] Updated message limits for threadId: ${this.threadId} (maxMessages: ${this._messageConfig.maxMessages}`;

    if (this._messageConfig.hoursBack === null) {
      logMessage += `, hoursBack: no time limit)`;
    } else {
      logMessage += `, hoursBack: ${this._messageConfig.hoursBack}h)`;
    }

    console.log(logMessage);

    // Reset the cache to force reloading with new limits
    this._lastLoadTime = 0;
  }

  /**
   * Message history configuration
   * These settings control how many messages are loaded from the database
   */
  private readonly _messageConfig = {
    // Maximum number of messages to retrieve from history
    // 100 messages provides good context while maintaining performance
    maxMessages: 30,

    // Time filter for messages (in hours)
    // null means no time filter - retrieve messages regardless of age
    // This ensures we don't lose context from older conversations
    hoursBack: null as number | null
  };

  /**
   * Load messages from the database with performance optimizations
   * This method applies limits to prevent loading too many messages,
   * which improves performance for long-running conversations
   */
  private async loadMessagesFromDB(): Promise<void> {
    // If already loading, wait for existing load to complete
    if (this._isLoading) {
      if (this._loadPromise) {
        await this._loadPromise;
        return;
      }
    }

    this._isLoading = true;

    console.log(`[PGMessageHistory:loadMessagesFromDB] Loading messages for threadId: ${this.threadId} (limit: ${this._messageConfig.maxMessages}, timeframe: ${this._messageConfig.hoursBack}h)`);

    this._loadPromise = new Promise<void>(async (resolve, reject) => {
      try {
        // Use the optimized getMessages method with limits
        const messages = await this.messageHistoryRepo.getMessages(
          this.threadId,
          {
            limit: this._messageConfig.maxMessages,
            hoursBack: this._messageConfig.hoursBack
          }
        );

        console.log(`[PGMessageHistory:loadMessagesFromDB] Loaded ${messages.length} messages for threadId: ${this.threadId}`);

        this._messages = messages.map(msg => {
          if (msg.role === 'user') {
            return new HumanMessage(msg.content);
          } else if (msg.role === 'assistant') {
            return new AIMessage(msg.content);
          } else if (msg.role === 'system') {
            return new SystemMessage(msg.content);
          }
          console.log(`[PGMessageHistory:loadMessagesFromDB] Unknown role: ${msg.role}, defaulting to AI message`);
          return new AIMessage(msg.content); // Default to AI message if role is unknown
        });

        console.log(`[PGMessageHistory:loadMessagesFromDB] Converted ${this._messages.length} messages to BaseMessage objects`);

        // Update the last load time
        this._lastLoadTime = Date.now();

        resolve();
      } catch (error) {
        console.error(`[PGMessageHistory:loadMessagesFromDB] Error loading messages:`, error);
        this._messages = []; // Initialize with empty array on error
        reject(error);
      } finally {
        this._isLoading = false;
        this._loadPromise = null;
      }
    });

    await this._loadPromise;
  }
}