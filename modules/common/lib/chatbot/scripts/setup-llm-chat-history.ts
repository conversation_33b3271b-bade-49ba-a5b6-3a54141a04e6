import "reflect-metadata";
import { config } from "dotenv";
if (process.env.NODE_ENV === "development") {
  config({ path: ".env.local" });
} else {
  config();
}

import { container } from "tsyringe";
import { PostgresPool } from "../memory";
import { TABLE_NAMES } from "../../../base/types/typing";

const TABLE_NAME = TABLE_NAMES.LLM_CHAT_HISTORY;
const THREAD_ID_INDEX = `idx_${TABLE_NAME}_thread_id`
const CREATED_AT_INDEX = `idx_${TABLE_NAME}_created_at`;

const run = async () => {
  const PgPool = container.resolve(PostgresPool);
  const pool = PgPool.pool;

  console.log(`Setting up ${TABLE_NAME} table...`);

  try {
    // Create the table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS ${TABLE_NAME} (
        id SERIAL PRIMARY KEY,
        thread_id VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        role VARCHAR(50) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    console.log("Table created successfully, creating indexes...");

    // Create indexes
    await pool.query(`
      CREATE INDEX IF NOT EXISTS ${THREAD_ID_INDEX} ON ${TABLE_NAME}(thread_id);
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS ${CREATED_AT_INDEX} ON ${TABLE_NAME}(created_at);
    `);

    console.log(`Successfully set up ${TABLE_NAME} table and indexes!`);
  } catch (error) {
    console.error(`Error setting up ${TABLE_NAME} table:`, error);
  }
};

run().catch(console.error);
