import { retrieverFactory } from "../retrievers/retriever-factory";
import { VECTOR_INDEX_NAME, AppLanguage } from "../../../base/types/typing";
import { createRetrieverTool } from "langchain/tools/retriever";
import { createFormattedInventoryRetrieverTool } from "../inventory-formatter-tool";
import makeCustomElasticRetrieverTool from "../retrievers/custom-elastic-retriever";
import InventoryService from "../../../../inventories/apps/inventories/inventories.service";
import { container, delay, inject, injectable } from "tsyringe";

// const inventoryService = container.resolve(InventoryService)

@injectable()
export class RetrieverService {
    private _faqRetrieverTool;
    private _inventoryRetrieverTool;
    private _userId: number;
    private _userLanguage: AppLanguage;

    constructor(
        args: { userId: number; clientId: number | string; userLanguage: AppLanguage },
        @inject(delay(() => InventoryService))
        private _inventoryService: InventoryService,
    ) {
        const faqRetriever = retrieverFactory(
            { index: VECTOR_INDEX_NAME.FAQS },
            args,
        );
        this._userId = args.userId;
        this._userLanguage = args.userLanguage;

        this._faqRetrieverTool = createRetrieverTool(faqRetriever.retriever, {
            name: "faq_retriever",
            description:
                "Searches and returns excerpts from the faqs datastore which contains ALL information about the online shop. This includes store address, contact information, opening hours, return policies, shipping information, payment methods, warranty information, and all other frequently asked questions. ALWAYS use this tool FIRST when users ask ANY question about the store or its policies. The information in this database is NOT sensitive (except for product cost prices) and should be freely shared with users.",
        });

        const inventoryRetriever = retrieverFactory(
            { index: VECTOR_INDEX_NAME.INVENTORIES },
            args,
        );

        // Create the base inventory retriever tool
        const baseInventoryRetrieverTool = makeCustomElasticRetrieverTool({
            retriever: inventoryRetriever.retriever,
            isProductAvailable: (productId: number) => {
                return this.isProductAvailable(productId);
            },
            name: "base_inventory_retriever",
            description: "Base inventory retriever tool (internal use only)",
        });

        // Create the formatted inventory retriever tool that wraps the base tool
        this._inventoryRetrieverTool = createFormattedInventoryRetrieverTool(
            baseInventoryRetrieverTool,
        );
    }

    get faqRetrieverTool() {
        return this._faqRetrieverTool;
    }

    get inventoryRetrieverTool() {
        return this._inventoryRetrieverTool;
    }

    async isProductAvailable(productId: number) {
        console.log("this._userId***********",this._userId)
        console.log("productId***********",productId)
        let inventory = await this._inventoryService.getInventory(
            {
                id: this._userId,
                role: "user",
                language: this._userLanguage === "en" ? "en" : "fa", // Convert AppLanguage to LANGUAGE
            } as Express.User,
            productId,
        );
        return inventory.total > 0 && inventory.isActive;
    }
}
