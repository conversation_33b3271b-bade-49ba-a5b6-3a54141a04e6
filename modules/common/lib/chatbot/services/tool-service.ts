import { Order } from "../../../../orders/apps/orders/types";
import { ClientOrderResponse } from "../../../../orders/apps/orders/types/schemas";
import { createModifyCartTool, createCartInfoTool, createCardOrderConfirmTool } from "../cart-tools";
import { createInventoryImageSenderTool } from "../inventory-tools";
import { createGetOrderStatusTool } from "../order-tools";
import { createFormattedOrderStatusTool } from "../order-formatter-tool";
import { RetrieverService } from "./retriever-service";
import { AppLanguage } from "../../../base/types/typing";

export class ToolService {
    private retrieverService: RetrieverService;

    constructor(
        private sendInventoryImage: (id: string) => Promise<boolean>,
        retrieverService: RetrieverService,
        private language: AppLanguage,
    ) {
        this.retrieverService = retrieverService;
    }

    createTools(
        modifyCard: (
            id: string,
            action: "add" | "remove",
            quantity: number,
        ) => Promise<string>,
        getCartInfo: () => Promise<string>,
        cardOrderConfirm: (confirm: boolean) => Promise<string>,
        getUserOrders: (userId: number) => Promise<ClientOrderResponse[]>,
    ) {
        const faqRetrieverTool = this.retrieverService.faqRetrieverTool;
        const inventoryRetrieverTool = this.retrieverService.inventoryRetrieverTool;

        // Update the description to emphasize the correct order of tool usage
        const originalDescription = inventoryRetrieverTool.description;
        inventoryRetrieverTool.description =
            `${originalDescription} IMPORTANT: You MUST use this tool BEFORE using the inventoryImageSender tool. Always retrieve product information first, then use the exact ID from the results to send an image.`;

        const inventoryImageSenderTool = createInventoryImageSenderTool(
            this.sendInventoryImage,
        );

        const cartManagerTool = createModifyCartTool(modifyCard);
        const cartInfoTool = createCartInfoTool(getCartInfo);
        const cardOrderConfirmTools = createCardOrderConfirmTool(cardOrderConfirm);

        // Create the base order status tool
        const baseOrderStatusTool = createGetOrderStatusTool(
            (userId: number) => {
                return getUserOrders(userId);
            },
            this.language
        );

        // Create the formatted order status tool that wraps the base tool
        const orderStatusInfoTool = createFormattedOrderStatusTool(baseOrderStatusTool, this.language);

        // Order is important - put inventory retriever before image sender
        // to encourage the correct sequence of operations
        return [
            faqRetrieverTool,
            inventoryRetrieverTool, // Must be used before inventoryImageSender
            inventoryImageSenderTool,
            cartManagerTool,
            cartInfoTool,
            cardOrderConfirmTools,
            orderStatusInfoTool
        ];
    }
}
