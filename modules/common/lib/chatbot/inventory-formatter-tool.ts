import { z } from "zod";
import { processInventoryResults } from "./utils/inventory-formatter";
import makeCustomElasticRetrieverTool from "./retrievers/custom-elastic-retriever";

/**
 * Create a wrapper for the inventory retriever tool that formats the results
 * @param inventoryRetrieverTool The original inventory retriever tool
 * @returns A new tool that wraps the inventory retriever and formats its results
 */
export function createFormattedInventoryRetrieverTool(
    inventoryRetrieverTool: any,
) {
    // Define the schema for the tool
    const schema = z.object({
        query: z
            .string()
            .describe("The search query to find products in the inventory"),
    });

    // Create a custom tool that wraps the inventory retriever
    return {
        name: "inventory_retriever",
        description:
            "Searches and returns information about available products. ALWAYS use this tool when users ask about available products or what products the store has. The results will include product IDs which are REQUIRED when using the inventoryImageSender tool.",
        schema: schema,
        invoke: async (input: { query: string }) => {
            try {
                // Call the original inventory retriever tool
                console.log(
                    `[FormattedInventoryRetriever] Calling inventory retriever with query: ${input.query}`,
                );
                const rawResults:string[] = await inventoryRetrieverTool.invoke({
                    query: input.query,
                });

                console.log(
                    `[FormattedInventoryRetriever] Raw results: ${rawResults.length} items found`,
                    JSON.stringify(rawResults.slice(0, 3), null, 2),
                );
                // const formattedResults = processInventoryResults(rawResults.);
                return rawResults.join("\n\n");
            } catch (error) {
                console.error("[FormattedInventoryRetriever] Error:", error);
                throw error;
            }
        },
    };
}
