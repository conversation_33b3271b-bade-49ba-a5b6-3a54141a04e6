import { RunnableConfig } from "@langchain/core/runnables";
import { AIMessage, BaseMessage, HumanMessage } from "@langchain/core/messages";
import { <PERSON>back<PERSON>and<PERSON> } from "langfuse-langchain";
import { AgentExecutor } from "langchain/agents";
import { container, inject } from "tsyringe";

import { CartService } from "./services/cart-service";
import { ToolService } from "./services/tool-service";
import { PGMessageHistory } from "./memory/pg-message-history";
import { MessageHistoryRepo } from "./repositories/message-history.repo";
import { PostgresPool } from "./memory";
import { RedisConnection } from "../redis";
import OrdersService from "../../../orders/apps/orders/order.service";
import InventoryService from "../../../inventories/apps/inventories/inventories.service";
import { RetrieverService } from "./services/retriever-service";

import CategoryService from "../../../categories/apps/categories/categories.service";
import CategoryRepo from "../../../categories/apps/categories/categories.repo";

import { loadExecutor } from "./utils/executor-loader";
import { PromptType, getPrompt } from "./utils/prompt-loader";

import {
    summarizerPromptTemplateEn,
    summarizerPromptTemplateFa,
} from "../../base/constant/prompts/language";
import { llmFactory } from "./llm-factory";
import {
    AppLanguage,
    CART_ACTION,
    CHAT_MODEL,
    CardHelperFunctions,
    ChatBoMessageResponse,
} from "../../base/types/typing";
import { MessageUtils } from "./utils";

const PgPool = container.resolve(PostgresPool);

// Add constant for maximum session idle time
const MAX_SESSION_IDLE_TIME = 30 * 60 * 1000; // 30 minutes in milliseconds

// Maximum number of categories to display in chatbot responses
const MAX_CATEGORIES_DISPLAY = 10;

// Platform account configuration
const PLATFORM_ACCOUNT_USER_IDS = (process.env.PLATFORM_ACCOUNT_USER_ID || "0")
    .split(",")
    .map((id) => parseInt(id.trim()))
    .filter((id) => !isNaN(id));

const LANG_FUSE_LANGCHAIN_HANDLER = new CallbackHandler({
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    baseUrl: process.env.LANGFUSE_HOST_URL,
    flushAt: 1, // cookbook-only: do not batch events, send them immediately
});

export class RAG {
    private _executor!: AgentExecutor;
    private _messageHistory: PGMessageHistory;
    private _cartService: CartService;
    private _executorInitialized: Promise<void> | null = null;
    private _lastInteractionTime: number; // Track when the last interaction occurred
    private _sessionState: Map<string, any> = new Map(); // Store session state
    private _resetTimeout: NodeJS.Timeout | null = null;

    constructor(
        @inject(RedisConnection) private redisConnection: RedisConnection,
        private readonly args: {
            userId: number;
            clientId: number | string;
            helpers: CardHelperFunctions;
            userLanguage: AppLanguage; // Add user language to constructor args
        },
        @inject(OrdersService) private ordersService: OrdersService,
        @inject(InventoryService) private inventoryService: InventoryService,
    ) {
        const threadId = args.clientId.toString();
        console.log(
            `[RAG:constructor] Creating RAG instance with threadId: ${threadId}`,
        );

        const messageHistoryRepo = new MessageHistoryRepo(PgPool.pool);
        console.log(
            `[RAG:constructor] Created MessageRepo with table: ${messageHistoryRepo["tableName"]}`,
        );

        this._messageHistory = new PGMessageHistory(
            threadId,
            messageHistoryRepo,
        );
        console.log(
            `[RAG:constructor] Created PGMessageHistory for threadId: ${threadId}`,
        );

        // Configure message history limits for better performance
        // Limit to 100 messages but with NO time limit to preserve conversation context
        this._messageHistory.configureMessageLimits({
            hoursBack: null, // No time limit - retrieve messages regardless of age to preserve context
        });

        this._cartService = new CartService(
            this.redisConnection,
            args.clientId.toString(),
            args.helpers,
        );
        console.log(
            `[RAG:constructor] Created CartService for clientId: ${args.clientId}`,
        );

        this._lastInteractionTime = Date.now();

        // Set up a session reset timer
        this._setupResetTimer();

        // Initialize the executor immediately
        this._initializeExecutor();
    }

    // Setup a timer to reset the session if idle for too long
    private _setupResetTimer() {
        if (this._resetTimeout) {
            clearTimeout(this._resetTimeout);
        }

        this._resetTimeout = setTimeout(() => {
            console.log(
                `[RAG] Session idle for ${MAX_SESSION_IDLE_TIME / 1000} seconds, resetting context...`,
            );
            this._resetSession();
        }, MAX_SESSION_IDLE_TIME);
    }

    // Reset session state
    private async _resetSession() {
        console.log(
            `[RAG] Resetting session for clientId: ${this.args.clientId}`,
        );
        this._sessionState.clear();
        // We're not clearing history, just resetting the session state
        // If needed, you could clear history too with: await this._messageHistory.clear();
    }

    // Private method to initialize the executor
    private async _initializeExecutor() {
        if (!this._executorInitialized) {
            this._executorInitialized = this.initialize();
        }
        try {
            await this._executorInitialized;
            console.log("[RAG] Executor initialized successfully");
        } catch (err) {
            console.error("[RAG] Error initializing executor:", err);
            this._executorInitialized = null;
        }
    }

    isPlatformAccount() {
        return PLATFORM_ACCOUNT_USER_IDS.includes(this.args.userId);
    }

    // Helper method to convert AppLanguage to LANGUAGE enum
    private getUserLanguageEnum() {
        return this.args.userLanguage === "fa" ? "fa" : "en";
    }

    async initialize() {
        const retrieverService = new RetrieverService(
            this.args,
            this.inventoryService,
        );

        // Check if this is the platform account
        const _isPlatformAccount = this.isPlatformAccount();
        let tools: any[] = [];
        if (!_isPlatformAccount) {
            // Only create tools for regular users, not platform account
            const toolService = new ToolService(
                this.args.helpers.sendInventoryImage,
                retrieverService,
                this.getUserLanguageEnum() as any,
            );

            tools = toolService.createTools(
                (id, action, quantity) => {
                    return this._cartService.modifyCart(
                        id,
                        action as CART_ACTION,
                        quantity,
                        this.getUserLanguageEnum() as any,
                    );
                },
                () => {
                    return this._cartService.getCartInfo(
                        this.getUserLanguageEnum() as any,
                    );
                },
                (confirm) => {
                    return this._cartService.manageCartOrderConfirm(
                        confirm,
                        this.getUserLanguageEnum() as any,
                    );
                },
                async (_userId) => {
                    const clientId = parseInt(this.args.clientId.toString());
                    if (isNaN(clientId)) {
                        console.error(
                            `[RAG] Invalid clientId: ${this.args.clientId}`,
                        );
                        return [];
                    }
                    console.log(
                        `[RAG] Getting orders for clientId: ${clientId}`,
                    );
                    return await this.ordersService.getClientOrders(clientId);
                },
            );
        }

        // Get categories for the user (only for non-platform accounts)
        let categories = "";
        if (!_isPlatformAccount) {
            try {
                const categoryRepo = new CategoryRepo();
                const categoryService = new CategoryService(categoryRepo);
                categories = await categoryService.getCategoriesForPrompt(
                    this.args.userId,
                    this.args.userLanguage,
                    MAX_CATEGORIES_DISPLAY,
                );
                console.log(
                    `[RAG:initialize] Retrieved categories for user ${this.args.userId}: ${categories.substring(0, 100)}...`,
                );
            } catch (error) {
                console.error(
                    `[RAG:initialize] Error getting categories for user ${this.args.userId}:`,
                    error,
                );
                categories =
                    this.args.userLanguage === "fa"
                        ? "دسته‌بندی‌ها در دسترس نیستند."
                        : "Categories are not available.";
            }
        }

        const promptTemplate: PromptType = _isPlatformAccount
            ? PromptType.PALETTE
            : PromptType.SALES_AGENT;
        // Get the system prompt for the sales agent with explicit language and categories
        const systemPrompt = await getPrompt(promptTemplate, {
            userId: this.args.userId,
            lang: this.args.userLanguage,
            categories: categories,
        });

        this._executor = await loadExecutor({
            tools,
            prompt: systemPrompt,
            maxIterations: 10,
            verbose: false,
        });
    }

    async invoke(message: string): Promise<string> {
        // Update last interaction time
        this._lastInteractionTime = Date.now();
        this._setupResetTimer();

        // Make sure the executor is initialized
        if (!this._executor) {
            console.log(
                "[RAG] Executor not initialized yet, initializing now...",
            );
            await this.initialize();
            console.log("[RAG] Executor initialized on demand");
        }

        const config: RunnableConfig = {
            callbacks: [LANG_FUSE_LANGCHAIN_HANDLER],
            runId: this.args.clientId.toString(),
            configurable: {
                thread_id: this.args.clientId.toString(),
                user_id: this.args.userId,
                session_state: Object.fromEntries(this._sessionState), // Pass session state to LLM
            },
        };

        try {
            // Load previous messages
            const previousMessages = await this._messageHistory.getMessages();
            console.log(
                `[RAG:invoke] Using ${previousMessages.length} previous messages for context`,
            );

            // Get current cart state for context
            const cartInfo = await this._cartService.getCartInfo(
                this.getUserLanguageEnum() as any,
            );
            console.log(
                `[RAG:invoke] Current cart state: ${JSON.stringify(cartInfo)}`,
            );

            let contextPrompt = "";

            if (!this.isPlatformAccount()) {
                contextPrompt += `
Current cart state: ${JSON.stringify(cartInfo)}
User message: ${message}
      `;
            }

            // Prepare additional context
            const contextMessage = new HumanMessage(contextPrompt);

            // Create input with all context
            const response = await this._executor.invoke(
                {
                    input: contextMessage.content,
                    chat_history: previousMessages,
                },
                config,
            );

            // Extract any updated session state
            if (response.intermediateSteps) {
                for (const step of response.intermediateSteps) {
                    if (
                        step.observation &&
                        typeof step.observation === "object" &&
                        step.observation.session_state
                    ) {
                        // Update our session state from tool outputs
                        for (const [key, value] of Object.entries(
                            step.observation.session_state,
                        )) {
                            this._sessionState.set(key, value);
                        }
                    }
                }
            }

            return response.output as string;
        } catch (error) {
            console.error("[RAG:invoke] Error invoking executor:", error);
            return "I'm sorry, I encountered an error processing your request. Please try again later.";
        }
    }

    async invokeWithHistory(message: string): Promise<BaseMessage[]> {
        console.log(
            `[RAG:invokeWithHistory] Processing message with history: ${message}`,
        );

        try {
            // Check if we need to restore context
            const timeSinceLastInteraction =
                Date.now() - this._lastInteractionTime;
            if (timeSinceLastInteraction > 60000) {
                // If more than 1 minute has passed
                console.log(
                    `[RAG:invokeWithHistory] Time since last interaction: ${timeSinceLastInteraction}ms. Restoring context...`,
                );

                // Get current cart state
                const cartInfo = await this._cartService.getCartInfo(
                    this.getUserLanguageEnum() as any,
                );
                if (cartInfo && Object.keys(cartInfo).length > 0) {
                    // Add a system message to remind the bot of current context
                    await this._messageHistory.addAIMessage(
                        `Just to keep track, currently you're helping with a shopping session. Cart contains: ${JSON.stringify(cartInfo)}`,
                    );
                }
            }

            console.log(
                `[RAG:invokeWithHistory] Adding user message to history`,
            );
            await this._messageHistory.addUserMessage(message);

            console.log(`[RAG:invokeWithHistory] Invoking LLM`);
            let response = await this.invoke(message);

            // Check if response exceeds Instagram's 1000 character limit
            if (response.length > 1000) {
                console.log(
                    `[RAG:invokeWithHistory] Response exceeds Instagram's 1000 character limit (${response.length} chars), summarizing...`,
                );
                response = await this._summarizeText(response);
            }

            console.log(
                `[RAG:invokeWithHistory] Adding AI response to history: ${response.substring(0, 50)}...`,
            );
            await this._messageHistory.addAIMessage(response);

            console.log(
                `[RAG:invokeWithHistory] Getting all messages from history`,
            );
            const allMessages = await this._messageHistory.getMessages();
            console.log(
                `[RAG:invokeWithHistory] Retrieved ${allMessages.length} messages from history`,
            );

            return allMessages;
        } catch (error) {
            console.error(
                `[RAG:invokeWithHistory] Error processing message with history:`,
                error,
            );
            throw error;
        }
    }

    async _processWithLLM(messages: BaseMessage[]): Promise<AIMessage> {
        // Make sure the executor is initialized
        if (!this._executor) {
            console.log(
                "[RAG] Executor not initialized yet, initializing now...",
            );
            await this.initialize();
            console.log("[RAG] Executor initialized on demand");
        }

        try {
            // Update the last interaction time
            this._lastInteractionTime = Date.now();
            this._setupResetTimer();

            // Get current cart state for context
            const cartInfo = await this._cartService.getCartInfo(
                this.getUserLanguageEnum() as any,
            );

            // Add cart context to the messages
            const messagesWithContext = [
                ...messages,
                new HumanMessage(
                    `Current cart state: ${JSON.stringify(cartInfo)}`,
                ),
            ];

            const response = await this._executor.invoke({
                input: messagesWithContext,
                chat_history: messages.slice(0, -1), // Exclude the last message which is in input
            });

            const aiMessage = response.output as AIMessage;

            // Check if response exceeds Instagram's 1000 character limit
            if (
                typeof aiMessage.content === "string" &&
                aiMessage.content.length > 1000
            ) {
                console.log(
                    `[RAG:_processWithLLM] Response exceeds Instagram's 1000 character limit (${aiMessage.content.length} chars), summarizing...`,
                );
                const summarizedContent = await this._summarizeText(
                    aiMessage.content,
                );
                return new AIMessage(summarizedContent);
            }

            return aiMessage;
        } catch (error) {
            console.error(
                "[RAG:_processWithLLM] Error invoking executor:",
                error,
            );
            return new AIMessage(
                "I'm sorry, I encountered an error processing your request. Please try again later.",
            );
        }
    }

    /**
     * Summarize text to keep it under Instagram's 1000 character limit
     * @param text The text to summarize
     * @returns Summarized text
     */
    async _summarizeText(text: string): Promise<string> {
        console.log(
            `[RAG:_summarizeText] Summarizing text of length ${text.length}`,
        );

        // If text is already under the limit, return it as is
        if (text.length <= 1000) {
            console.log(
                `[RAG:_summarizeText] Text already under limit, no summarization needed`,
            );
            return text;
        }

        try {
            // Create a summarizer model
            const summarizer = llmFactory(process.env.CHATBOT as CHAT_MODEL);

            console.log(
                `[RAG:_summarizeText] Using user language for summarization: ${this.args.userLanguage}`,
            );

            // Use the language-specific summarizer prompt template
            const summarizerTemplate =
                this.args.userLanguage === "en"
                    ? summarizerPromptTemplateEn
                    : summarizerPromptTemplateFa;

            // Format the prompt with the text to summarize
            const prompt = await summarizerTemplate.format({ text });

            // Invoke the summarizer
            const response = await summarizer.invoke(prompt);

            const summarizedText = response.content as string;
            console.log(
                `[RAG:_summarizeText] Summarized text from ${text.length} to ${summarizedText.length} characters`,
            );

            // If still over limit, truncate with ellipsis
            if (summarizedText.length > 1000) {
                console.log(
                    `[RAG:_summarizeText] Summarized text still over limit, truncating`,
                );
                return summarizedText.substring(0, 997) + "...";
            }

            return summarizedText;
        } catch (error) {
            console.error(
                `[RAG:_summarizeText] Error summarizing text:`,
                error,
            );
            // If summarization fails, truncate the original text
            return text.substring(0, 997) + "...";
        }
    }

    /**
     * Main method to process user messages (for backward compatibility)
     */
    processMessage = async (message: ChatBoMessageResponse) => {
        try {
            // Update the last interaction time
            this._lastInteractionTime = Date.now();
            this._setupResetTimer();

            // Create a properly formatted message
            const userMessage = MessageUtils.createMessage(
                message.type,
                message.text,
            );
            if (!userMessage) {
                return "I'm sorry, I couldn't process your message. Please try again.";
            }

            console.log(
                `[RAG:processMessage] Processing message and saving to history: ${message.text}`,
            );

            // Process with LLM using invokeWithHistory to save messages to the database
            const messagesWithHistory = await this.invokeWithHistory(
                message.text,
            );

            // Get the last message (AI response)
            const lastMessage =
                messagesWithHistory[messagesWithHistory.length - 1];
            let responseContent = lastMessage.content as string;

            console.log(
                `[RAG:processMessage] Response saved to history: ${responseContent.substring(0, 50)}...`,
            );

            // Check if response exceeds Instagram's 1000 character limit
            if (responseContent.length > 1000) {
                console.log(
                    `[RAG:processMessage] Response exceeds Instagram's 1000 character limit (${responseContent.length} chars), summarizing...`,
                );
                responseContent = await this._summarizeText(responseContent);
            }

            return responseContent;
        } catch (error) {
            console.error(
                `[RAG:processMessage] Error processing message:`,
                error,
            );
            console.error(`[RAG:processMessage] Returning fallback response.`);
            return "I'm sorry, I encountered an error processing your request. Please try again later.";
        }
    };
}
