import { AppLanguage } from "../../../base/types/typing";

/**
 * Generates appropriate error message when requested quantity exceeds total stock
 * @param requestedQuantity The quantity user requested
 * @param totalStock The total stock quantity (not considering reserved)
 * @param language The user's language preference
 * @param productName Optional product name for more specific messaging
 * @returns Error message in appropriate language
 */
export function getInsufficientStockMessage(
    requestedQuantity: number,
    totalStock: number,
    language: AppLanguage,
    productName?: string
): string {
    if (totalStock === 0) {
        // No stock available
        return language === "en"
            ? `Sorry, this product is currently out of stock.`
            : `متاسفانه این محصول در حال حاضر موجود نیست.`;
    }

    if (totalStock > 0) {
        // Some stock available but less than requested
        if (language === "en") {
            return `Sorry, we only have ${totalStock} item${totalStock > 1 ? 's' : ''} available${productName ? ` for ${productName}` : ''}. You requested ${requestedQuantity}.`;
        } else {
            return `متاسفانه از این محصول تنها ${totalStock} عدد موجود است${productName ? ` (${productName})` : ''}. شما ${requestedQuantity} عدد درخواست کرده‌اید.`;
        }
    }

    // Fallback message
    return language === "en"
        ? "The requested quantity is not available."
        : "تعداد درخواستی موجود نیست.";
}

/**
 * Validates if requested quantity is available in total stock
 * @param requestedQuantity The quantity user wants to add
 * @param totalStock The total stock quantity (not considering reserved)
 * @returns Object with validation result and available quantity
 */
export function validateStockQuantity(
    requestedQuantity: number,
    totalStock: number
): {
    isValid: boolean;
    availableQuantity: number;
    canPartiallyFulfill: boolean;
} {
    return {
        isValid: requestedQuantity <= totalStock,
        availableQuantity: totalStock,
        canPartiallyFulfill: totalStock > 0 && requestedQuantity > totalStock,
    };
}
