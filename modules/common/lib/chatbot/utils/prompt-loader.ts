import { SystemMessage } from "@langchain/core/messages";
import {
    shoppingInstructionPromptEn,
    cartPromptEn,
    orderPromptEn,
    inventoryPromptEn,
    conversationStatePromptEn,
} from "../../../base/constant/prompts/language/en";
import {
    shoppingInstructionPromptFa,
    cartPromptFa,
    orderPromptFa,
    inventoryPromptFa,
    conversationStatePromptFa,
} from "../../../base/constant/prompts/language/fa";

import { AppLanguage } from "../../../base/types/typing";
import { container } from "tsyringe";
import { UserLanguageService } from "../../services/user-language.service";

interface PromptOptions {
    userId: string | number;
}
export enum PromptType {
    SALES_AGENT = "sales_agent",
    PALETTE = "palette",
}

/**
 * Get a prompt for the specified type
 * @param promptType The type of prompt to get
 * @param options Options for customizing the prompt
 * @returns A SystemMessage containing the prompt
 */
export async function getPrompt(
    promptType: PromptType,
    options: PromptOptions & { lang?: AppLanguage; persona?: string, categories?: string },
): Promise<SystemMessage> {
    console.log(`[prompt-loader] Getting prompt for type: ${promptType}`);

    // Determine language from options or fallback to system default
    const optionsLang = options.lang;
    const userLanguageService = container.resolve(UserLanguageService);
    const fallbackLang = userLanguageService.getSystemLanguage();

    // Log detailed information about language selection
    console.log(
        `[prompt-loader] Language from options: ${optionsLang || "not provided"}`,
    );
    console.log(
        `[prompt-loader] Fallback language from service: ${fallbackLang}`,
    );

    // Use provided language or fallback to system language
    const finalLang = optionsLang || fallbackLang;

    // Select final language
    const lang: AppLanguage = finalLang;
    console.log(`[prompt-loader] Final language selected: ${lang}`);

    try {
        switch (promptType) {
            case PromptType.SALES_AGENT:
                // Select the appropriate prompts based on language
                let shoppingInstructionByLanguage =
                    lang === "en" ? shoppingInstructionPromptEn : shoppingInstructionPromptFa;

                // Replace categories placeholder if categories are provided
                if(options.categories){
                    shoppingInstructionByLanguage = new SystemMessage(
                        shoppingInstructionByLanguage.content.toString().replaceAll(
                            "{categories}",
                            options.categories
                        )
                    );
                }
               
                    const cartPromptByLang =
                    lang === "en" ? cartPromptEn : cartPromptFa;
                const orderPromptByLang =
                    lang === "en" ? orderPromptEn : orderPromptFa;
                const inventoryPromptByLang =
                    lang === "en"
                        ? inventoryPromptEn
                        : inventoryPromptFa;
                const conversationStatePromptByLang =
                    lang === "en"
                        ? conversationStatePromptEn
                        : conversationStatePromptFa;

                // Combine all prompts for the sales agent
                const combinedContent = combinePrompts([
                    extractContent(shoppingInstructionByLanguage),
                    extractContent(cartPromptByLang),
                    extractContent(orderPromptByLang),
                    extractContent(inventoryPromptByLang),
                    extractContent(conversationStatePromptByLang),
                ]);

                // Add user-specific information
                const customizedContent = `USER_ID: ${options.userId}\n\n${combinedContent}`;

                console.log(
                    `[prompt-loader] Created combined prompt (${customizedContent.length} chars)`,
                );
                return new SystemMessage(customizedContent);

            case PromptType.PALETTE:
                return new SystemMessage(getPlatformWelcomePrompt(lang));

            default:
                // Default to using just the RAG prompt based on language
                const defaultPrompt =
                    lang === "en" ? shoppingInstructionPromptEn : shoppingInstructionPromptFa;
                const content = extractContent(defaultPrompt);
                return new SystemMessage(content);
        }
    } catch (error) {
        console.error("[prompt-loader] Error creating prompt:", error);
        return createFallbackPrompt(lang);
    }
}

export function getPlatformWelcomePrompt(lang: AppLanguage): string {
    const prompts = {
        en: `Welcome to Palette Platform! 🎨

I'm your AI assistant here to help you understand our e-commerce platform. I can answer questions about features, usage, and best practices.

## Quick Start Guide:
• **Product Management**: Add, edit, and organize your inventory
• **Order Processing**: Track and manage customer orders
• **Customer Support**: Handle inquiries and support tickets
• **Analytics**: View sales reports and customer insights
• **Settings**: Configure your store preferences

## Frequently Asked Questions:

**Q: How do I add new products?**
A: Navigate to Products → Add New Product. Fill in the required details like name, price, description, and upload images.

**Q: How can I track orders?**
A: Go to Orders section to view all orders with their status. You can filter by date, status, or customer.

**Q: How do I handle customer support?**
A: Check the Support section for customer inquiries. You can respond directly or escalate to appropriate departments.

**Q: Can I customize my store appearance?**
A: Yes! Go to Settings → Store Design to customize colors, layout, and branding elements.

**Q: How do I view sales analytics?**
A: Visit the Analytics dashboard for comprehensive reports on sales, customers, and product performance.

**Q: What payment methods are supported?**
A: We support major credit cards, PayPal, and various digital payment methods. Configure them in Settings → Payments.

**Q: How do I manage inventory levels?**
A: Product inventory is automatically tracked. Set low-stock alerts in Settings → Inventory.

**Q: Can I export my data?**
A: Yes, you can export orders, customers, and products data from their respective sections.

Feel free to ask me anything about using the platform! I'm here to help make your e-commerce journey smooth and successful.

`,

        fa: `به پلتفرم پلت خوش آمدید! 🎨

من دستیار هوشمند شما هستم و آماده کمک برای درک پلتفرم تجارت الکترونیک ما می‌باشم. می‌توانم به سوالات شما درباره ویژگی‌ها، نحوه استفاده و بهترین روش‌ها پاسخ دهم.

## راهنمای شروع سریع:
• **مدیریت محصولات**: افزودن، ویرایش و سازماندهی موجودی
• **پردازش سفارشات**: پیگیری و مدیریت سفارشات مشتریان
• **پشتیبانی مشتریان**: رسیدگی به درخواست‌ها و تیکت‌های پشتیبانی
• **آنالیتیکس**: مشاهده گزارش‌های فروش و بینش مشتریان
• **تنظیمات**: پیکربندی تنظیمات فروشگاه

## سوالات متداول:

**س: چگونه محصول جدید اضافه کنم؟**
ج: به بخش محصولات → افزودن محصول جدید بروید. جزئیات مورد نیاز مانند نام، قیمت، توضیحات را پر کنید و تصاویر آپلود کنید.

**س: چگونه سفارشات را پیگیری کنم؟**
ج: به بخش سفارشات بروید تا تمام سفارشات را با وضعیت‌شان مشاهده کنید. می‌توانید بر اساس تاریخ، وضعیت یا مشتری فیلتر کنید.

**س: چگونه پشتیبانی مشتریان را انجام دهم؟**
ج: بخش پشتیبانی را برای درخواست‌های مشتریان بررسی کنید. می‌توانید مستقیماً پاسخ دهید یا به بخش‌های مربوطه ارجاع دهید.

**س: آیا می‌توانم ظاهر فروشگاهم را شخصی‌سازی کنم؟**
ج: بله! به تنظیمات → طراحی فروشگاه بروید تا رنگ‌ها، چیدمان و عناصر برندینگ را شخصی‌سازی کنید.

**س: چگونه آمار فروش را مشاهده کنم؟**
ج: از داشبورد آنالیتیکس برای گزارش‌های جامع فروش، مشتریان و عملکرد محصولات استفاده کنید.

**س: چه روش‌های پرداختی پشتیبانی می‌شود؟**
ج: از کارت‌های اعتباری اصلی، پی‌پال و روش‌های پرداخت دیجیتال مختلف پشتیبانی می‌کنیم. آن‌ها را در تنظیمات → پرداخت‌ها پیکربندی کنید.

**س: چگونه سطح موجودی را مدیریت کنم؟**
ج: موجودی محصولات به طور خودکار پیگیری می‌شود. هشدارهای موجودی کم را در تنظیمات → موجودی تنظیم کنید.

**س: آیا می‌توانم داده‌هایم را خروجی بگیرم؟**
ج: بله، می‌توانید داده‌های سفارشات، مشتریان و محصولات را از بخش‌های مربوطه خروجی بگیرید.

در مورد استفاده از پلتفرم از من هر سوالی بپرسید! من اینجا هستم تا سفر تجارت الکترونیک شما را آسان و موفق کنم.`,
    };

    return prompts[lang] || prompts.en;
}

/**
 * Combine multiple prompts into a single string
 * @param prompts Array of prompt strings
 * @returns Combined prompt string
 */
function combinePrompts(prompts: string[]): string {
    const filteredPrompts = prompts.filter(Boolean);
    console.log(`[prompt-loader] Combining ${filteredPrompts.length} prompts`);
    return filteredPrompts.join("\n\n");
}

/**
 * Extract content from a prompt object
 * @param prompt The prompt object
 * @returns The content of the prompt as a string
 */
function extractContent(prompt: any): string {
    if (!prompt) return "";

    // If it's already a string, return it
    if (typeof prompt === "string") return prompt;

    // If it's an object, try to extract the content
    if (typeof prompt === "object") {
        // Try different properties that might contain the content
        if ("content" in prompt && typeof prompt.content === "string") {
            return prompt.content;
        }

        if ("_content" in prompt && typeof prompt._content === "string") {
            return prompt._content;
        }

        // For older versions of LangChain
        if ("text" in prompt && typeof prompt.text === "string") {
            return prompt.text;
        }

        // Try toString as a last resort
        try {
            const str = prompt.toString();
            if (str && str !== "[object Object]") {
                return str;
            }
        } catch (e) {
            // Ignore toString errors
        }
    }

    return "";
}

/**
 * Create a fallback prompt if everything else fails
 * @param lang The language to use for the fallback prompt
 * @returns A SystemMessage with a basic prompt
 * @throws Error if lang is not provided
 */
function createFallbackPrompt(lang: AppLanguage): SystemMessage {
    // Check if language is provided
    if (!lang) {
        const userLanguageService = container.resolve(UserLanguageService);
        lang = userLanguageService.getSystemLanguage();
    }

    console.log(
        `[prompt-loader:createFallbackPrompt] Creating fallback prompt with language: ${lang}`,
    );

    // Log system language for debugging
    const userLanguageService = container.resolve(UserLanguageService);
    console.log(
        `[prompt-loader:createFallbackPrompt] System language from service: ${userLanguageService.getSystemLanguage()}`,
    );

    if (lang === "en") {
        console.log(
            `[prompt-loader:createFallbackPrompt] Using English fallback prompt`,
        );
        return new SystemMessage(
            "You are a helpful sales assistant. Help the customer find products and place orders. " +
                "Always respond in English language. Be polite, friendly, and professional.",
        );
    } else {
        console.log(
            `[prompt-loader:createFallbackPrompt] Using Persian fallback prompt`,
        );
        return new SystemMessage(
            "You are a helpful sales assistant. Help the customer find products and place orders. " +
                "Always respond in Persian (Farsi) language. Be polite, friendly, and professional.",
        );
    }
}
