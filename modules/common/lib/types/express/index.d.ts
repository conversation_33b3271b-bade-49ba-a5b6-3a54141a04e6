export declare global {
    namespace Express {
        export interface Request {
            profile?: Profile;
            parsedQuery: Partial<Query>;
            language: import("../../../base/types/typing").AppLanguage; // Non-optional since middleware guarantees it
        }

        export interface Response {
            success(data: unknown, message?: string, statusCode?: number): void;
            chunked(
                contentLength: number,
                contentType: string,
                statusCode?: number,
            ): void;
        }

        export interface User {
            id: number;
            role: string;
            language: string; // Non-optional since database guarantees it with default "fa"
        }

        export type SortOrder = "ASC" | "DESC";

        export interface Query {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            filter: Record<string, any>;
            search: string;
            searchField: string | string[];
            sort: Record<string, SortOrder>;
            page: number;
            pageSize: number;
        }

        export interface Profile {
            provider: string;
            name: string;
            email: string;
        }
    }
}
