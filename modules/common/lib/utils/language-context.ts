/**
 * Language context utilities
 * Provides helpers for getting user language
 */

import { AppLanguage } from "../../base/types/typing";

/**
 * Get language from user context (works with both User entity and Express.User)
 * @param user The user object
 * @returns The user's language
 */
export function getUserLanguage(user: { language: string } | Express.User): AppLanguage {
    return user.language as AppLanguage;
}
