/**
 * Text formatting utilities
 * Contains functions for formatting text for display
 */

import { AppLanguage } from "../../base/types/typing";
import { Request } from "express";

/**
 * Format a price based on the request language context
 * @param price The price to format (string or number)
 * @param req The Express request object (with language context)
 * @returns The formatted price with appropriate currency
 */
export function formatPrice(price: string | number, req: Request): string;

/**
 * Format a price based on the specified language
 * @param price The price to format (string or number)
 * @param language The language to use for formatting
 * @returns The formatted price with appropriate currency
 */
export function formatPrice(price: string | number, language: AppLanguage): string;

/**
 * Implementation for both overloads
 */
export function formatPrice(price: string | number, reqOrLanguage: Request | AppLanguage): string {
  let currentLanguage: AppLanguage;
  
  if (typeof reqOrLanguage === 'string') {
    // It's a language
    currentLanguage = reqOrLanguage;
  } else {
    // It's a request object, get language directly from req.language
    currentLanguage = reqOrLanguage.language || "fa";
  }
  
  // Parse the price to a number
  let numericPrice: number;
  if (typeof price === 'string') {
    // Remove any non-numeric characters except decimal point
    const cleanedPrice = price.replace(/[^\d.]/g, '');
    numericPrice = parseFloat(cleanedPrice);
  } else {
    numericPrice = price;
  }

  // Check if the price is a valid number
  if (isNaN(numericPrice)) {
    return currentLanguage === "fa" ? 'قیمت نامشخص' : 'Price unknown';
  }

  // Format based on language
  if (currentLanguage === "fa") {
    // Format for Persian: use Toman
    return `${numericPrice.toLocaleString()} تومان`;
  } else {
    // Format for English: use Dollar
    return `$${numericPrice.toFixed(2)}`;
  }
}
