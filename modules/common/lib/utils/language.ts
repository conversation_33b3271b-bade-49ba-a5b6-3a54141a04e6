/**
 * Language utility functions
 * @deprecated This entire file is deprecated. Use language detection middleware instead.
 */

import { AppLanguage } from "../../base/types/typing";

/**
 * Validate and normalize a language code
 * @param lang The language code to validate
 * @returns A valid language ("en" or "fa")
 */
export function validateLanguage(lang: string | null | undefined): AppLanguage {
  return lang === "en" ? "en" : "fa";
}