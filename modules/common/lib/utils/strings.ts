import crypto from "crypto";

import { utils } from "..";

export function hashToDigits(str: string, digits: number) {
    const m = Math.pow(10, digits + 1) - 1;
    const phi = Math.pow(10, digits) / 2 - 1;
    let n = 0;
    for (let i = 0; i < str.length; i++) {
        n = (n + phi * str.charCodeAt(i)) % m;
    }
    return n.toString();
}

/**
 * @description Generate a URL-Safe token that can be used for one-time usage
 */
export function generateToken(length?: number): string {
    length = length ?? 32;

    const randomBytes = crypto.randomBytes(length);

    return randomBytes.toString("hex").toUpperCase();
}

export function getNextName(names: string[], baseName: string): string {
    // Extract numbers from the names
    const numbers = names
        .map((name) => {
            const match = name.match(new RegExp(`^${baseName}\\((\\d+)\\)$`));
            return utils.isNotNil(match)
                ? parseInt(match[1], 10)
                : name === baseName
                    ? 0
                    : -1;
        })
        .filter((num) => num >= 0)
        .sort((a, b) => a - b);

    // Find the next number
    const nextNumber = numbers.length > 0 ? numbers[numbers.length - 1] + 1 : 1;

    // Generate the next name
    return nextNumber === 1 && !names.includes(baseName)
        ? baseName
        : `${baseName}(${nextNumber})`;
}

export function arabicToPersian(input: string) {
    const diff = {
        ك: "ک",
        دِ: "د",
        بِ: "ب",
        زِ: "ز",
        ذِ: "ذ",
        شِ: "ش",
        سِ: "س",
        ى: "ی",
        ي: "ی",
        ئ: "ی",
        "١": "۱",
        "٢": "۲",
        "٣": "۳",
        "٤": "۴",
        "٥": "۵",
        "٦": "۶",
        "٧": "۷",
        "٨": "۸",
        "٩": "۹",
        "٠": "۰",
    };

    for (const [key, value] of Object.entries(diff)) {
        input = input.replaceAll(key, value);
    }

    return input;
}

export function rtlToLtrNumber(str: string) {
    str = arabicToPersian(str);
    const numbers = [
        /۰/g,
        /۱/g,
        /۲/g,
        /۳/g,
        /۴/g,
        /۵/g,
        /۶/g,
        /۷/g,
        /۸/g,
        /۹/g,
    ];

    for (let i = 0; i < 10; i++) {
        str = str.replace(numbers[i], `${i}`);
    }

    return str;
}



/**
 * Sanitizes an ID by removing spaces, underscores, hyphens, line breaks, and other special characters
 * This is particularly useful for SMS services like Kavenegar that have restrictions on token formats
 * @param id The ID to sanitize
 * @returns The sanitized ID
 */
export function sanitizeContent(text: string | number): string {
    if (text === null || text === undefined) {
        return '';
    }

    // Convert to string and remove all non-alphanumeric characters
    // This is more thorough than just removing specific characters
    // Only keep numbers and letters to ensure compatibility with SMS services or everywhere needs   
    return text.toString().replace(/[^a-zA-Z0-9]/g, '');
}
