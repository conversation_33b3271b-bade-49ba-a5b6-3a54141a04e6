import { NextFunction, Request, Response } from "express";

import { utils } from "..";
import { getSuccessMessage, SuccessMessageKey } from "../errors/locales/success-messages";
import { AppLanguage } from "../../base/types/typing";

// Define a constant for the default success message
const DEFAULT_SUCCESS_MESSAGE = "DEFAULT_SUCCESS_MESSAGE" as const;

type ResponseData = {
    metadata?: unknown;
    data: unknown[];
};

export function responseHandler(
    req: Request,
    res: Response,
    next: NextFunction,
) {
    res.success = (data: ResponseData, message = DEFAULT_SUCCESS_MESSAGE, statusCode = 200) => {
        // If the default message is used, translate it based on the user's language from request context
        if (message === DEFAULT_SUCCESS_MESSAGE) {
            message = getSuccessMessage(DEFAULT_SUCCESS_MESSAGE as SuccessMessageKey, req.language);
        }
        if (utils.isNotNil(data?.metadata)) {
            return res.status(statusCode).json({
                success: true,
                metadata: data.metadata,
                data: data.data,
                message,
            });
        }

        res.status(statusCode).json({
            success: true,
            data,
            message,
        });
    };

    res.chunked = (contentLength, contentType, statusCode = 200) => {
        const headers = {
            "Accept-Ranges": "bytes",
            "Content-Length": contentLength,
            "Content-Range": `bytes 0-${contentLength - 1}/${contentLength}`,
            "Content-Type": contentType,
        };
        res.writeHead(statusCode, headers);
    };

    next();
}
