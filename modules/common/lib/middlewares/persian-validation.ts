import { Request, Response, NextFunction } from 'express';
import { validatePersianText } from '../utils/data-normalizer';

/**
 * Configuration for Persian text validation middleware
 */
export interface PersianValidationConfig {
    /** Fields to validate and normalize */
    fields?: string[];
    /** Whether to validate all string fields if no specific fields are provided */
    validateAllStrings?: boolean;
    /** Whether to throw an error on validation failure or just normalize */
    throwOnError?: boolean;
    /** Custom error message */
    errorMessage?: string;
    /** Whether to apply validation to request body */
    validateBody?: boolean;
    /** Whether to apply validation to query parameters */
    validateQuery?: boolean;
    /** Whether to apply validation to route parameters */
    validateParams?: boolean;
}

/**
 * Custom error class for Persian validation errors
 */
export class PersianValidationError extends Error {
    constructor(message: string, public field?: string, public originalValue?: any) {
        super(message);
        this.name = 'PersianValidationError';
    }
}

/**
 * Validates and normalizes Persian text in request data
 * @param config Configuration for validation behavior
 * @returns Express middleware function
 */
export function persianValidationMiddleware(config: PersianValidationConfig = {}) {
    const {
        fields = [],
        validateAllStrings = true,
        throwOnError = false,
        errorMessage = 'Invalid Persian text format',
        validateBody = true,
        validateQuery = true,
        validateParams = false
    } = config;

    return (req: Request, res: Response, next: NextFunction) => {
        try {
            // Validate and normalize request body
            if (validateBody && req.body) {
                req.body = validateAndNormalizeObject(req.body, fields, validateAllStrings, throwOnError, errorMessage);
            }

            // Validate and normalize query parameters
            if (validateQuery && req.query) {
                req.query = validateAndNormalizeObject(req.query, fields, validateAllStrings, throwOnError, errorMessage);
            }

            // Validate and normalize route parameters
            if (validateParams && req.params) {
                req.params = validateAndNormalizeObject(req.params, fields, validateAllStrings, throwOnError, errorMessage);
            }

            next();
        } catch (error) {
            if (error instanceof PersianValidationError) {
                return res.status(400).json({
                    error: 'Validation Error',
                    message: error.message,
                    field: error.field,
                    originalValue: error.originalValue
                });
            }
            next(error);
        }
    };
}

/**
 * Validates and normalizes an object's string properties
 * @param obj The object to validate
 * @param fields Specific fields to validate (empty array means all string fields)
 * @param validateAllStrings Whether to validate all string fields
 * @param throwOnError Whether to throw errors or just normalize
 * @param errorMessage Custom error message
 * @returns The validated and normalized object
 */
function validateAndNormalizeObject(
    obj: any,
    fields: string[],
    validateAllStrings: boolean,
    throwOnError: boolean,
    errorMessage: string
): any {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }

    const result = Array.isArray(obj) ? [...obj] : { ...obj };

    // Determine which fields to validate
    const fieldsToValidate = fields.length > 0 
        ? fields 
        : (validateAllStrings ? Object.keys(result).filter(key => typeof result[key] === 'string') : []);

    for (const field of fieldsToValidate) {
        if (result[field] !== undefined && result[field] !== null) {
            const originalValue = result[field];
            
            try {
                // Apply Persian text validation
                const normalizedValue = validatePersianText(originalValue);
                
                // Check if the value changed significantly (indicating potential issues)
                if (throwOnError && typeof originalValue === 'string' && originalValue !== normalizedValue) {
                    const hasSignificantChange = checkForSignificantChanges(originalValue, normalizedValue);
                    if (hasSignificantChange) {
                        throw new PersianValidationError(
                            `${errorMessage}: Field '${field}' contains invalid characters`,
                            field,
                            originalValue
                        );
                    }
                }
                
                result[field] = normalizedValue;
            } catch (error) {
                if (throwOnError) {
                    throw new PersianValidationError(
                        `${errorMessage}: Failed to validate field '${field}'`,
                        field,
                        originalValue
                    );
                }
                // If not throwing errors, just apply basic normalization
                result[field] = validatePersianText(originalValue);
            }
        }
    }

    // Recursively validate nested objects and arrays
    for (const key in result) {
        if (result[key] && typeof result[key] === 'object') {
            if (Array.isArray(result[key])) {
                result[key] = result[key].map((item: any) => 
                    validateAndNormalizeObject(item, fields, validateAllStrings, throwOnError, errorMessage)
                );
            } else {
                result[key] = validateAndNormalizeObject(result[key], fields, validateAllStrings, throwOnError, errorMessage);
            }
        }
    }

    return result;
}

/**
 * Checks if the normalization caused significant changes that might indicate invalid input
 * @param original The original string
 * @param normalized The normalized string
 * @returns True if there are significant changes
 */
function checkForSignificantChanges(original: string, normalized: string): boolean {
    // Remove whitespace for comparison
    const originalTrimmed = original.trim();
    const normalizedTrimmed = normalized.trim();
    
    // If the length difference is more than 20% of the original, consider it significant
    const lengthDifference = Math.abs(originalTrimmed.length - normalizedTrimmed.length);
    const significantLengthChange = lengthDifference > (originalTrimmed.length * 0.2);
    
    // Check for removal of too many characters (might indicate corrupted input)
    const removedCharacters = originalTrimmed.length - normalizedTrimmed.length;
    const tooManyRemovedCharacters = removedCharacters > (originalTrimmed.length * 0.3);
    
    return significantLengthChange || tooManyRemovedCharacters;
}

/**
 * Decorator function for applying Persian validation to specific routes
 * @param config Validation configuration
 * @returns Decorator function
 */
export function ValidatePersianText(config: PersianValidationConfig = {}) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;
        
        descriptor.value = function (...args: any[]) {
            const [req, res, next] = args;
            
            // Apply validation middleware
            const middleware = persianValidationMiddleware(config);
            middleware(req, res, (error?: any) => {
                if (error) {
                    return next(error);
                }
                // Call the original method
                return method.apply(this, args);
            });
        };
        
        return descriptor;
    };
}

/**
 * Simple function to validate a single string value
 * @param value The string to validate
 * @param throwOnError Whether to throw an error on validation failure
 * @returns The validated and normalized string
 */
export function validateSinglePersianText(value: string, throwOnError: boolean = false): string {
    try {
        const normalized = validatePersianText(value);
        
        if (throwOnError && checkForSignificantChanges(value, normalized)) {
            throw new PersianValidationError('Invalid Persian text format', undefined, value);
        }
        
        return normalized;
    } catch (error) {
        if (throwOnError) {
            throw error;
        }
        return validatePersianText(value);
    }
}
