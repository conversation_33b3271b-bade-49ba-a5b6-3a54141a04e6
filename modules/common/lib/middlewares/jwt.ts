import { NextFunction, Request, Response } from "express";
import passport from "passport";
import { container } from "tsyringe";

import { UnauthorizedError } from "../errors";
import { isNil, isNotNil } from "../utils";
import { AppLanguage } from "../../base/types/typing";
import { UserLanguageService } from "../services/user-language.service";

export function JWT(req: Request, res: Response, next: NextFunction) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    passport.authenticate(
        "jwt",
        async (err: unknown, user: Express.User | undefined) => {
            if (isNotNil(err)) {
                return next(err);
            }
            if (!user || isNil(user)) {
                return next(new UnauthorizedError());
            }
            
            // Set user in request
            req.user = user;
            
            // Check if language was already set by global detectLanguage middleware
            // If X-Language header was provided, respect it; otherwise use database preference
            const userLanguageService = container.resolve(UserLanguageService);
            const headerLanguage = req.headers['x-language'] as string;
            
            if (headerLanguage && userLanguageService.isValidLanguage(headerLanguage)) {
                // Header language takes priority - already set by global middleware
                console.log(`[JWT] Keeping header language ${req.language} for user ${user.id}`);
            } else {
                // No header provided, get from database
                req.language = await userLanguageService.getUserLanguage(user.id);
                console.log(`[JWT] Using database language ${req.language} for user ${user.id}`);
            }
            
            return next();
        },
    )(req, res, next);
}
