import { NextFunction, Request, Response } from "express";
import { container } from "tsyringe";
import { AppLanguage } from "../../base/types/typing";
import { UserLanguageService } from "../services/user-language.service";

/**
 * Global language detection middleware
 * Runs on every request to set req.language based on:
 * 1. X-Language header (always checked first)
 * 2. User database preference (if user is authenticated)
 * 3. Default fallback
 * 
 * This middleware should be added globally in app.ts
 */
export function detectLanguage(req: Request, res: Response, next: NextFunction) {
    const userLanguageService = container.resolve(UserLanguageService);
    
    // Always check X-Language header first
    const headerLanguage = req.headers['x-language'] as string;
    
    if (headerLanguage && userLanguageService.isValidLanguage(headerLanguage)) {
        req.language = headerLanguage as AppLanguage;
        console.log(`[GlobalLanguageDetection] Using header language ${req.language}`);
        return next();
    }
    
    // If no valid header, check if user is authenticated (will be set by JWT middleware later)
    // For now, just set default - JWT middleware will override with DB language if needed
    req.language = userLanguageService.getDefaultLanguage();
    console.log(`[GlobalLanguageDetection] Using default language ${req.language} (may be overridden by JWT)`);
    
    return next();
}
