import { singleton } from "tsyringe";
import { Kavenegar, SendResponse } from "kavenegar-client";
import { sanitizeContent } from "../utils/strings";

// SMS Template Constants
const SMS_TEMPLATES = {
    SIGNUP_VERIFICATION: "signupVerification",
    SELLER_NOTIFICATION: "sellerNotification",
    SUBSCRIPTION_EXPIRY: "subscriptionExpiry"
} as const;

@singleton()
export class KavenegarService {
    private _client: Kavenegar;

    constructor() {
        this._client = new Kavenegar(process.env.KAVENEGAR_API_KEY);
    }

    async sendOtp(phone: string, otp: string): Promise<SendResponse> {
        // Apply sanitization to ensure the OTP doesn't contain invalid characters
        const sanitizedOtp = sanitizeContent(otp);

        console.log('[KavenegarService] Sending OTP with sanitized value:', {
            original: otp,
            sanitized: sanitizedOtp,
            phone,
            template: SMS_TEMPLATES.SIGNUP_VERIFICATION
        });

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedOtp,
                template: SMS_TEMPLATES.SIGNUP_VERIFICATION,
            });

            console.log('[KavenegarService] Successfully sent OTP SMS:', {
                phone,
                otp: sanitizedOtp,
                response
            });

            return response;
        } catch (error: any) {
            console.error('[KavenegarService] Failed to send OTP SMS:', {
                phone,
                otp: sanitizedOtp,
                error: error?.message || 'Unknown error',
                stack: error?.stack
            });
            throw error;
        }
    }

    async sendSellerNotification(
        phone: string,
        orderId: string,
        userName: string,
    ): Promise<SendResponse> {
        // Apply sanitization directly in the service to ensure it's always done
        const sanitizedOrderId = sanitizeContent(orderId);
        const sanitizedUserName = sanitizeContent(userName);

        console.log('[KavenegarService] Sending seller notification SMS with sanitized orderId:', {
            original: orderId,
            sanitizedOrderId: sanitizedOrderId,
            sanitizedUserName: sanitizedUserName,
            phone,
            template: SMS_TEMPLATES.SELLER_NOTIFICATION
        });

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedOrderId,
                token2: sanitizedUserName,
                template: SMS_TEMPLATES.SELLER_NOTIFICATION,
            });

            console.log('[KavenegarService] Successfully sent seller notification SMS:', {
                phone,
                orderId: sanitizedOrderId,
                response
            });

            return response;
        } catch (error: any) {
            console.error('[KavenegarService] Failed to send seller notification SMS:', {
                phone,
                orderId: sanitizedOrderId,
                error: error?.message || 'Unknown error',
                stack: error?.stack
            });
            throw error;
        }
    }

    async sendSubscriptionExpiryNotification(
        phone: string,
        userName: string,
        planName: string,
        remainingDays: string,
    ): Promise<SendResponse> {
        // Apply sanitization to ensure the content doesn't contain invalid characters
        const sanitizedUserName = sanitizeContent(userName);
        const sanitizedPlanName = sanitizeContent(planName);
        const sanitizedRemainingDays = sanitizeContent(remainingDays);

        console.log('[KavenegarService] Sending subscription expiry notification SMS:', {
            phone,
            userName: sanitizedUserName,
            planName: sanitizedPlanName,
            remainingDays: sanitizedRemainingDays,
            template: SMS_TEMPLATES.SUBSCRIPTION_EXPIRY
        });

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedUserName,
                token2: sanitizedPlanName,
                token3: sanitizedRemainingDays,
                template: SMS_TEMPLATES.SUBSCRIPTION_EXPIRY,
            });

            console.log('[KavenegarService] Successfully sent subscription expiry notification SMS:', {
                phone,
                userName: sanitizedUserName,
                planName: sanitizedPlanName,
                remainingDays: sanitizedRemainingDays,
                response
            });

            return response;
        } catch (error: any) {
            console.error('[KavenegarService] Failed to send subscription expiry notification SMS:', {
                phone,
                userName: sanitizedUserName,
                planName: sanitizedPlanName,
                remainingDays: sanitizedRemainingDays,
                error: error?.message || 'Unknown error',
                stack: error?.stack
            });
            throw error;
        }
    }
}
