import { DataSource } from "typeorm";

import { utils } from "..";
import { UserSchema } from "../../../users/apps/users/users.model";
import { FileSchema } from "../../../static-server/apps/static-server/static-server.model";
import { CategorySchema } from "../../../categories/apps/categories/categories.model";
import { InventorySchema } from "../../../inventories/apps/inventories/inventories.model";
import { AutomationSchema } from "../../../automation/apps/automation/automation.model";
import { ConditionSchema } from "../../../automation/apps/condition/condition.model";
import { ChatSchema } from "../../../chat/apps/chat/chat.model";
import { ClientSchema } from "../../../users/apps/client/client.model";
import { WebsiteSchema } from "../../../websites/apps/websites/website.model";
import { UserWebsiteSchema } from "../../../user-websites/apps/user-websites/user-website.model";
import { ChannelSchema } from "../../../channels/apps/channels/channel.model";
import { AttributeSchema } from "../../../inventories/apps/attributes/attribute.model";
import { OrderSchema } from "../../../orders/apps/orders/order.model";
import { OrderItemsSchema } from "../../../order-items/apps/order-items/order-items.model";
import { UserSubscriptionSchema } from "../../../subscriptions/apps/user-subscriptions/user-subscription.model";
import { SubscriptionPlanSchema } from "../../../subscriptions/apps/subscriptions-plan/subscription-plan.model";
import { ActionSchema } from "../../../automation/apps/action/action.model";
import { FAQSchema } from "../../../faq/apps/faq/faq.model";
import { AdminSchema } from "../../../admin/apps/admin/admin.model";
import { NotificationSchema } from "../../../notifications/apps/notifications/notification.model";
import { NotificationConfigSchema } from "../../../notifications/apps/notifications-config/notifications-config.model";
import { TransactionSchema } from "../../../transactions/apps/transactions/transaction.model";

const {
    NODE_ENV,
    POSTGRES_DB_HOST,
    POSTGRES_DB_PORT,
    POSTGRES_DB_NAME,
    POSTGRES_DB_USER,
    POSTGRES_DB_PASSWORD,
} = process.env;

export class AppDataSource {
    private static _instance: DataSource;
    constructor() {}

    public static getInstance(): DataSource {
        if (utils.isNil(AppDataSource._instance)) {
            AppDataSource._instance = new DataSource({
                type: "postgres",
                host: POSTGRES_DB_HOST,
                port: Number(POSTGRES_DB_PORT),
                username: POSTGRES_DB_USER,
                password: POSTGRES_DB_PASSWORD,
                database: POSTGRES_DB_NAME,
                entities: [
                    AdminSchema,
                    UserSchema,
                    CategorySchema,
                    FileSchema,
                    AutomationSchema,
                    ConditionSchema,
                    ChatSchema,
                    ClientSchema,
                    InventorySchema,
                    WebsiteSchema,
                    UserWebsiteSchema,
                    ChannelSchema,
                    AttributeSchema,
                    OrderSchema,
                    OrderItemsSchema,
                    UserSubscriptionSchema,
                    SubscriptionPlanSchema,
                    ActionSchema,
                    FAQSchema,
                    NotificationSchema,
                    NotificationConfigSchema,
                    TransactionSchema,
                ],
                synchronize: NODE_ENV === "test",
            });
        }

        return AppDataSource._instance;
    }
}
