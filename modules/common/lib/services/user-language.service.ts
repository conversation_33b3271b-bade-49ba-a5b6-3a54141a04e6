/**
 * User Language Service
 * Handles user language-related operations
 */

import { singleton } from "tsyringe";
import { container } from "tsyringe";
import { Request } from "express";
import { AppLanguage } from "../../base/types/typing";
import { PostgresPool } from "../chatbot/memory";

/**
 * Language detection priority configuration
 * 
 * EASY CONFIGURATION CHANGE:
 * To change priority order, modify HEADER_FIRST:
 * - true: Header first, then database (current setting)
 * - false: Database first, then header
 * 
 * USAGE:
 * - Login routes: Use detectLanguage middleware (header only)
 * - Authenticated routes: Use JWT middleware (header + database with priority)
 */
const LANGUAGE_DETECTION_PRIORITY = {
    HEADER_FIRST: true, // Set to false to prioritize database over header
};

@singleton()
export class UserLanguageService {
    private readonly pgPool: PostgresPool;

    constructor() {
        this.pgPool = container.resolve(PostgresPool);
    }

    /**
     * Get user's language with configurable priority
     * @param userId The user ID (for database lookup)
     * @param req Express request object (for header lookup)
     * @returns The user's language preference
     */
    async getUserLanguageWithPriority(userId: number, req: Request): Promise<AppLanguage> {
        if (LANGUAGE_DETECTION_PRIORITY.HEADER_FIRST) {
            // Priority 1: Check X-Language header
            const headerLanguage = this.getLanguageFromHeader(req);
            if (headerLanguage) {
                console.log(`[UserLanguageService] Using header language ${headerLanguage} for user ${userId}`);
                return headerLanguage;
            }

            // Priority 2: Check database
            const dbLanguage = await this.getUserLanguage(userId);
            console.log(`[UserLanguageService] Using database language ${dbLanguage} for user ${userId}`);
            return dbLanguage;
        } else {
            // Alternative priority: Database first, then header
            const dbLanguage = await this.getUserLanguage(userId);
            const headerLanguage = this.getLanguageFromHeader(req);
            
            if (dbLanguage !== this.getDefaultLanguage()) {
                console.log(`[UserLanguageService] Using database language ${dbLanguage} for user ${userId}`);
                return dbLanguage;
            } else if (headerLanguage) {
                console.log(`[UserLanguageService] Using header language ${headerLanguage} for user ${userId}`);
                return headerLanguage;
            }
            
            return dbLanguage;
        }
    }

    /**
     * Extract language from X-Language header
     * @param req Express request object
     * @returns AppLanguage if header exists and valid, null otherwise
     */
    private getLanguageFromHeader(req: Request): AppLanguage | null {
        const headerValue = req.headers['x-language'] as string;
        
        if (headerValue && this.isValidLanguage(headerValue)) {
            return headerValue as AppLanguage;
        }
        
        return null;
    }

    /**
     * Get user's preferred language from database
     * @param userId The user ID
     * @returns The user's language preference or fallback to "fa"
     */
    async getUserLanguage(userId: number): Promise<AppLanguage> {
        try {
            const query = `
                SELECT language FROM "user" WHERE id = $1 LIMIT 1
            `;
            
            const result = await this.pgPool.pool.query(query, [userId]);
            
            if (result.rows.length > 0 && result.rows[0].language) {
                const language = result.rows[0].language as AppLanguage;
                console.log(`[UserLanguageService] Retrieved language ${language} for user ${userId}`);
                return language;
            }

            // User found but no language set, return default
            console.log(`[UserLanguageService] No language set for user ${userId}, using default: fa`);
            return "fa" as AppLanguage;
        } catch (error) {
            console.error(`[UserLanguageService] Error fetching language for user ${userId}:`, error);
            // Return default language on error
            return "fa" as AppLanguage;
        }
    }

    /**
     * Update user's language preference
     * @param userId The user ID
     * @param language The new language preference
     * @returns Promise<boolean> indicating success
     */
    async updateUserLanguage(userId: number, language: AppLanguage): Promise<boolean> {
        try {
            const query = `
                UPDATE "user" SET language = $1 WHERE id = $2
            `;
            
            await this.pgPool.pool.query(query, [language, userId]);
            console.log(`[UserLanguageService] Updated language to ${language} for user ${userId}`);
            return true;
        } catch (error) {
            console.error(`[UserLanguageService] Error updating language for user ${userId}:`, error);
            return false;
        }
    }

    /**
     * Get the default application language
     * @returns The default language
     */
    getDefaultLanguage(): AppLanguage {
        return "fa" as AppLanguage;
    }

    /**
     * Get system language for operations where no user context is available
     * Falls back to default language
     * @returns The system language
     */
    getSystemLanguage(): AppLanguage {
        return this.getDefaultLanguage();
    }

    /**
     * Validate if a language code is supported
     * @param language The language code to validate
     * @returns boolean indicating if the language is supported
     */
    isValidLanguage(language: string): language is AppLanguage {
        return language === "en" || language === "fa";
    }

    /**
     * Get a safe language value (validates and returns default if invalid)
     * @param language The language code to validate
     * @returns A valid AppLanguage
     */
    getSafeLanguage(language: string | null | undefined): AppLanguage {
        if (language && this.isValidLanguage(language)) {
            return language;
        }
        return this.getDefaultLanguage();
    }
}
