/**
 * Success messages for multilingual support
 * Contains both English and Persian translations
 */

import { AppLanguage } from "../../../base/types/typing";

/**
 * Type for success message keys
 */
export type SuccessMessageKey =
    | "USER_OTP_SENT_SUCCESSFULLY"
    | "USER_LOGGED_IN_SUCCESSFULLY"
    | "DEFAULT_SUCCESS_MESSAGE"
    | "INSTAGRAM_DISCONNECTED_SUCCESSFULLY";

/**
 * Success messages by language
 */
export const successMessages: Record<
    AppLanguage,
    Record<SuccessMessageKey, string>
> = {
    ["en"]: {
        USER_OTP_SENT_SUCCESSFULLY: "SMS sent successfully",
        USER_LOGGED_IN_SUCCESSFULLY: "You have logged in successfully",
        DEFAULT_SUCCESS_MESSAGE: "Operation completed successfully",
        INSTAGRAM_DISCONNECTED_SUCCESSFULLY: "Instagram account disconnected successfully",
    },
    ["fa"]: {
        USER_OTP_SENT_SUCCESSFULLY: "پیامک ارسال شد",
        USER_LOGGED_IN_SUCCESSFULLY: "شما با موفقیت وارد شدید",
        DEFAULT_SUCCESS_MESSAGE: "با موفقیت انجام شد",
        INSTAGRAM_DISCONNECTED_SUCCESSFULLY: "حساب اینستاگرام با موفقیت قطع شد",
    },
};

/**
 * Get a success message in the specified language
 * @param key The message key
 * @param language The language to get the message in
 * @returns The translated message
 */
export function getSuccessMessage(
    key: SuccessMessageKey,
    language: AppLanguage,
): string {
    return (
        successMessages[language]?.[key] || 
        successMessages["fa"][key] || 
        successMessages["en"].DEFAULT_SUCCESS_MESSAGE
    );
}
