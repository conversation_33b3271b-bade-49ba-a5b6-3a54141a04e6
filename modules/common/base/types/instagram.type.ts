// Define Instagram API URLs
export const INSTA_GRAPH_API = "https://graph.instagram.com";
export const INSTA_BASIC_API = "https://api.instagram.com";
export const INSTAGRAM_BASIC_DISPLAY_OAUTH_API =
    "https://www.instagram.com/oauth/authorize";

export interface InstagramProfileResponse {
    user_id: string;
    username: string;
    name: string;
}

export interface InstagramSuccessResponse {
    success: boolean;
}

export interface InstagramSendInventoryDto {
    name: string;
    image: string;
}

export interface InstagramAccessTokenResponse {
    access_token: string;
    user_id: string;
    permissions: string[];
}

export interface InstagramLongLivedTokenResponse {
    access_token: string;
    expires_in: number;
}

export interface InstagramChallengeDto {
    "hub.mode": "subscribe";
    "hub.challenge": string;
    "hub.verify_token": string;
}

// Old scopes !
//"business_basic",
//"business_manage_messages",
//"business_manage_comments",// Should Remove 
//"business_content_publish",// Should Remove 

//New scopes !
// "instagram_business_basic",
// "instagram_manage_messages",

// export const InstaGramScopes = [
//     "business_basic",
//     "business_manage_messages",
//     "business_manage_comments",
//     "business_content_publish",
// ];

export const InstaGramScopes = [
    "instagram_business_basic",
    "instagram_business_manage_messages",
];
