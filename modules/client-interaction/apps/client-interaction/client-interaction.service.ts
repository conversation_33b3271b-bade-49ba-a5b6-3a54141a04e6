import { injectable } from "tsyringe";
import ChatRepo from "../../../chat/apps/chat/chat.repo";
import { ClientInteractionStatus, ClientInteractionStatusResponse } from "./types";
import { utils } from "../../../common";
import Logger from "../../../common/lib/metrics/logger";

// 23 hours in milliseconds
const MESSAGING_WINDOW_HOURS = 23;
const MESSAGING_WINDOW_MS = MESSAGING_WINDOW_HOURS * 60 * 60 * 1000;

@injectable()
export default class ClientInteractionService {
    constructor(private _chatRepo: ChatRepo) {}

    async getClientInteractionStatus(clientId: number): Promise<ClientInteractionStatus> {
        Logger.info("Getting client interaction status", {
            action: "getClientInteractionStatus",
            clientId
        });

        try {
            // Get the last client interaction (user message only)
            const lastInteraction = await this._chatRepo.getLastClientInteraction(clientId);
            
            const now = new Date();
            let lastInteractionAt: Date | undefined;
            let hoursSinceLastInteraction: number | undefined;
            let messagingWindowExpiresAt: Date | undefined;
            let canReceiveMessages = false;
            let messagingWindowExpired = true;

            if (utils.isNotNil(lastInteraction)) {
                lastInteractionAt = new Date(lastInteraction.chat_createdAt);
                const timeDiffMs = now.getTime() - lastInteractionAt.getTime();
                hoursSinceLastInteraction = timeDiffMs / (1000 * 60 * 60); // Convert to hours
                
                // Calculate when the messaging window expires (23 hours after last interaction)
                messagingWindowExpiresAt = new Date(lastInteractionAt.getTime() + MESSAGING_WINDOW_MS);
                
                // Check if we're still within the 23-hour window
                messagingWindowExpired = timeDiffMs > MESSAGING_WINDOW_MS;
                canReceiveMessages = !messagingWindowExpired;

                Logger.info("Client interaction analysis", {
                    clientId,
                    lastInteractionAt: lastInteractionAt.toISOString(),
                    hoursSinceLastInteraction,
                    messagingWindowExpired,
                    canReceiveMessages
                });
            } else {
                Logger.info("No client interaction found", { clientId });
            }

            // Set restrictions based on messaging window status
            const restrictions = {
                canReceiveMessages: canReceiveMessages
            };

            return {
                clientId,
                lastInteractionAt,
                hoursSinceLastInteraction,
                messagingWindowExpired,
                messagingWindowExpiresAt,
                restrictions
            };

        } catch (error) {
            Logger.error("Error getting client interaction status", {
                clientId,
                error: error instanceof Error ? error.message : "Unknown error"
            });
            
            // Return safe defaults on error
            return {
                clientId,
                messagingWindowExpired: true,
                restrictions: {
                    canReceiveMessages: false
                }
            };
        }
    }

    async getClientInteractionStatusResponse(clientId: number): Promise<ClientInteractionStatusResponse> {
        const status = await this.getClientInteractionStatus(clientId);
        
        return {
            clientId: status.clientId,
            lastInteractionAt: status.lastInteractionAt?.toISOString(),
            hoursSinceLastInteraction: status.hoursSinceLastInteraction,
            messagingWindowExpired: status.messagingWindowExpired,
            messagingWindowExpiresAt: status.messagingWindowExpiresAt?.toISOString(),
            restrictions: status.restrictions
        };
    }

    /**
     * Check if a client can receive messages (utility method for other services)
     */
    async canClientReceiveMessages(clientId: number): Promise<boolean> {
        const status = await this.getClientInteractionStatus(clientId);
        return status.restrictions.canReceiveMessages;
    }

    /**
     * Get hours remaining in messaging window (utility method)
     */
    async getMessagingWindowHoursRemaining(clientId: number): Promise<number> {
        const status = await this.getClientInteractionStatus(clientId);
        
        if (!status.lastInteractionAt || status.messagingWindowExpired) {
            return 0;
        }
        
        return Math.max(0, MESSAGING_WINDOW_HOURS - (status.hoursSinceLastInteraction || 0));
    }
}
