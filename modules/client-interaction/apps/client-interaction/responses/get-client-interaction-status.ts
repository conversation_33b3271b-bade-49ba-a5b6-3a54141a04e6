import { JSONSchemaType } from "ajv";
import { ClientInteractionStatusResponse } from "../types";

export const GetClientInteractionStatusResponseSchema: JSONSchemaType<ClientInteractionStatusResponse> = {
    type: "object",
    properties: {
        clientId: { type: "number" },
        lastInteractionAt: {
            type: "string",
            format: "date-time",
            nullable: true
        },
        hoursSinceLastInteraction: {
            type: "number",
            nullable: true
        },
        messagingWindowExpired: { type: "boolean" },
        messagingWindowExpiresAt: {
            type: "string",
            format: "date-time",
            nullable: true
        },
        restrictions: {
            type: "object",
            properties: {
                canReceiveMessages: { type: "boolean" }
            },
            required: ["canReceiveMessages"]
        }
    },
    required: [
        "clientId",
        "messagingWindowExpired",
        "restrictions"
    ]
};
