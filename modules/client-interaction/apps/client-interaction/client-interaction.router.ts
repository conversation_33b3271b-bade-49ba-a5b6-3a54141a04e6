import express from "express";
import { container } from "tsyringe";
import ClientInteractionController from "./client-interaction.controller";
import { JWT } from "../../../common/lib/middlewares";

const router = express.Router();

const controller = container.resolve(ClientInteractionController);

router
    .route("/status/:clientId")
    .get(JWT, controller.getClientInteractionStatus);

export default router;
