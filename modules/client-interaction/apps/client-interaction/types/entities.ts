export interface ClientInteractionStatus {
    clientId: number;
    lastInteractionAt?: Date;
    hoursSinceLastInteraction?: number;
    messagingWindowExpired: boolean;
    messagingWindowExpiresAt?: Date;
    restrictions: {
        canReceiveMessages: boolean;
    };
}

export interface ClientInteractionStatusResponse {
    clientId: number;
    lastInteractionAt?: string;
    hoursSinceLastInteraction?: number;
    messagingWindowExpired: boolean;
    messagingWindowExpiresAt?: string;
    restrictions: {
        canReceiveMessages: boolean;
    };
}
