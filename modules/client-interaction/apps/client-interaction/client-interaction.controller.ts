import "reflect-metadata";
import { Request, Response } from "express";
import { injectable } from "tsyringe";
import ClientInteractionService from "./client-interaction.service";
import { errors, utils } from "../../../common";
import Logger from "../../../common/lib/metrics/logger";
import { OpenAPI } from "../../../common/lib/decorators/openapi";
import { GetClientInteractionStatusResponseSchema } from "./responses";

@injectable()
export default class ClientInteractionController {
    constructor(private _service: ClientInteractionService) {}

    @OpenAPI(
        "client-interaction",
        "/status/{clientId}",
        "get",
        undefined,
        [
            {
                in: "path",
                name: "clientId",
                schema: {
                    type: "string",
                    example: "123"
                }
            }
        ],
        GetClientInteractionStatusResponseSchema,
        "bearerAuth"
    )
    getClientInteractionStatus = async (req: Request, res: Response) => {
        try {
            const { clientId } = req.params;
            
            // Validate clientId parameter
            if (utils.isNil(clientId) || isNaN(Number(clientId))) {
                Logger.error("Invalid clientId parameter", {
                    action: "getClientInteractionStatus",
                    clientId,
                    error: "badRequestError"
                });
                throw new errors.BadRequestError();
            }

            const clientIdNumber = Number(clientId);
            
            Logger.info("Getting client interaction status", {
                action: "getClientInteractionStatus",
                clientId: clientIdNumber
            });

            const result = await this._service.getClientInteractionStatusResponse(clientIdNumber);

            Logger.info("Client interaction status retrieved successfully", {
                action: "getClientInteractionStatus",
                clientId: clientIdNumber,
                canReceiveMessages: result.restrictions.canReceiveMessages,
                messagingWindowExpired: result.messagingWindowExpired
            });

            res.status(200).json(result);
        } catch (error) {
            Logger.error("Error in getClientInteractionStatus controller", {
                action: "getClientInteractionStatus",
                clientId: req.params.clientId,
                error: error instanceof Error ? error.message : "Unknown error"
            });

            if (error instanceof errors.BadRequestError) {
                res.status(400).json({ 
                    error: "Bad Request", 
                    message: error.message 
                });
            } else {
                res.status(500).json({ 
                    error: "Internal Server Error", 
                    message: "Failed to get client interaction status" 
                });
            }
        }
    };
}
