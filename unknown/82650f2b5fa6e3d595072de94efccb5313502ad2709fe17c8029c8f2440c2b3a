import { REGISTRATION_METHOD } from "../../../../common/base/types/typing";

export interface User {
    id: number;
    phone: string;
    state: "OTP_NOT_VERIFIED" | "REGISTERED";
    email: string;
    registrationMethod: REGISTRATION_METHOD;
    role: string;
    createdAt: Date;
    updatedAt: Date;
    language: string;
    clientLimit: number | null;
}

export interface MessagingEvent {
    id: string;
    time: number;
    messaging: Messaging[];
}

export interface InstagramWebhookPayload {
    object: string;
    entry: {
        id: string;
        time: number;
        messaging: Messaging[];
    }[];
}

export interface Messaging {
    sender: {
        id: string;
    };
    recipient: {
        id: string;
    };
    timestamp: number;
    message?: InstagramMessageDto;
    reaction?: InstagramReactionDto;
    postback?: InstagramPostbackDto;
    referral?: InstagramReferralDto;
    read?: InstagramReadDto;
}

export interface InstagramMessageDto {
    mid: string; // ID of the message sent to your business
    text?: string; // Included when a customer sends a message containing text
    attachments?: Array<{
        type: string; // Can be audio, file, image, video, etc.
        payload: { url: string };
    }>;
    is_deleted?: boolean;
    is_echo?: boolean; // Included when your business sends a message to the customer
    quick_reply?: { payload: string };
    reply_to?: { mid: string };
}

export interface InstagramReactionDto {
    mid: string;
    action: "react" | "unreact";
    reaction?: string;
    emoji?: string;
}

export interface InstagramPostbackDto {
    mid: string;
    title: string;
    payload: string;
}

export interface InstagramReferralDto {
    ref: string;
    source: string;
    type: "OPEN_THREAD";
}

export interface InstagramReadDto {
    mid: string;
}

export interface AccessToken {
    token: string;
}

export interface VerifyInstagramWebhook {
    "hub.mode": string;
    "hub.verify_token": string;
    "hub.challenge": number;
}

export interface InstagramAuthData {
    clientId: string;
    clientSecret: string;
    grantType: string;
}
